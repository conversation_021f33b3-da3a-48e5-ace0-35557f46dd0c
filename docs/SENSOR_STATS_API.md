# Sensor Statistics API

## Overview
The Sensor Statistics API provides aggregated statistics about sensors in the system, including total count, online count, and offline count.

## Endpoint

### GET /api/sensors/sensor-stats

Returns aggregated statistics for all sensors in the system.

#### Response

```json
{
  "totalSensors": 128,
  "onlineSensors": 112,
  "offlineSensors": 16
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `totalSensors` | number | Total number of sensors (excluding deleted ones) |
| `onlineSensors` | number | Number of sensors with connectivity.state = 1 |
| `offlineSensors` | number | Number of sensors with connectivity.state ≠ 1 |

#### Status Codes

- `200 OK` - Successfully retrieved sensor statistics
- `500 Internal Server Error` - Server error occurred

## Implementation Details

### Connectivity State Logic
- **Online**: Sensors with `connectivity.state = 1`
- **Offline**: Sensors with `connectivity.state ≠ 1` (includes 0, 2, 3, etc.)

### Caching
- Results are cached for 5 minutes when `ENABLE_CACHE=true`
- Cache key: `sensor:stats`

### Database Query
The endpoint uses MongoDB aggregation pipeline to efficiently count sensors:

```javascript
[
  {
    $match: {
      isDeleted: { $ne: true }
    }
  },
  {
    $group: {
      _id: null,
      totalSensors: { $sum: 1 },
      onlineSensors: {
        $sum: {
          $cond: [{ $eq: ['$connectivity.state', 1] }, 1, 0]
        }
      },
      offlineSensors: {
        $sum: {
          $cond: [{ $ne: ['$connectivity.state', 1] }, 1, 0]
        }
      }
    }
  }
]
```

## Usage Examples

### cURL
```bash
curl -X GET "http://localhost:3000/api/sensors/sensor-stats" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### JavaScript/TypeScript
```typescript
const response = await fetch('/api/sensors/sensor-stats', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const stats = await response.json();
console.log(`Total: ${stats.totalSensors}, Online: ${stats.onlineSensors}, Offline: ${stats.offlineSensors}`);
```

### Response Example
```json
{
  "totalSensors": 128,
  "onlineSensors": 112,
  "offlineSensors": 16
}
```

## Error Handling

If no sensors exist in the system, the endpoint returns:
```json
{
  "totalSensors": 0,
  "onlineSensors": 0,
  "offlineSensors": 0
}
```

## Performance Considerations

- The query uses database indexes on `isDeleted` and `connectivity.state` fields
- Results are cached to reduce database load
- Aggregation pipeline is optimized for performance
- No pagination needed as it returns only summary statistics
