# CoDDN API Quick Start Guide

## 🚀 Get Up and Running in 5 Minutes

This guide will help you set up the CoDDN API for local development quickly.

## Prerequisites Checklist

- [ ] Node.js v20.9.0 or higher
- [ ] npm or yarn package manager
- [ ] MongoDB (local or cloud)
- [ ] Git
- [ ] Code editor (VS Code recommended)

## Step 1: Clone and Install

```bash
# Clone the repository
git clone <repository-url>
cd CoDDN_API

# Install dependencies
npm install
# or
yarn install

# Install Python dependencies (for spectrum analysis)
pip install -r ./pyScripts/requirements.txt
```

## Step 2: Environment Setup

Create a `.env.dev` file in the root directory:

```bash
# Copy the example environment file
cp .env.dev.example .env.dev
```

### Minimal Configuration (.env.dev)

```bash
# Server
ENV=dev
PORT=3000

# MongoDB (replace with your connection string)
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/coddn
DATABASE_NAME=coddn

# Spectrum Database (can be same as main DB for development)
SPECTRUM_MONGODB_CONNECTION_STRING=mongodb://localhost:27017/spectrum
SPECTRUM_DATABASE_NAME=spectrum_events_db

# Auth0 (get these from your Auth0 dashboard)
AUTH0_ISSUER_URL=https://your-auth0-domain.auth0.com
AUTH0_AUDIENCE=https://your-api-audience
AUTH0_CLIENT_ID=your-client-id
AUTH0_SECRET_KEY=your-secret-key

# Management API
AUTH0_MANAGEMENT_API=https://your-auth0-domain.auth0.com
AUTH0_MANAGEMENT_API_CLIENT_ID=your-management-client-id
AUTH0_MANAGEMENT_API_CLIENT_SECRET=your-management-secret

# Optional: Redis (will fallback to in-memory if not available)
REDIS_HOST=redis://localhost:6379
ENABLE_CACHE=true

# AWS (for production features - can be dummy values for local dev)
AWS_ACCESS_KEY_ID=dummy-key
AWS_SERCRET_KEY_ID=dummy-secret
AWS_REGION=us-east-2

# Kafka (for production features - can be dummy values for local dev)
MSK_BROKERS=["localhost:9092"]
GROUP_ID=devGroupApi
RID_UI_TOPIC=DETECTION_DEV
RID_TOPIC=RID_DEV

# AppSync (for production features - can be dummy values for local dev)
APP_SYNC_URL=https://dummy-endpoint/graphql
APP_SYNC_API_KEY=dummy-key

# Feature Flags
SPECTRUM_FLAG=false
```

## Step 3: Database Setup

### Option A: Local MongoDB

```bash
# Install MongoDB locally
# macOS
brew install mongodb-community

# Ubuntu
sudo apt-get install mongodb

# Start MongoDB
mongod --dbpath /path/to/your/db
```

### Option B: MongoDB Atlas (Cloud)

1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a cluster
3. Get connection string
4. Update `MONGODB_CONNECTION_STRING` in `.env.dev`

## Step 4: Auth0 Setup

1. **Create Auth0 Account**: Go to [Auth0](https://auth0.com)
2. **Create Application**:
   - Type: Single Page Application
   - Note the Domain, Client ID, and Client Secret
3. **Create API**:
   - Note the API Identifier (this is your audience)
4. **Update Environment Variables**:
   ```bash
   AUTH0_ISSUER_URL=https://your-domain.auth0.com
   AUTH0_AUDIENCE=your-api-identifier
   AUTH0_CLIENT_ID=your-client-id
   AUTH0_SECRET_KEY=your-client-secret
   ```

## Step 5: Run the Application

```bash
# Development mode with hot reload
npm run start:dev
# or
yarn start:dev
```

You should see:

```
Application is running on: http://localhost:3000
✅ Redis cache store connected successfully (if Redis is available)
🧠 Using in-memory cache store only (if Redis is not available)
```

## Step 6: Test the API

### Health Check

```bash
curl http://localhost:3000/
```

Expected response:

```json
Root path is skipped
```

### Test Authentication (requires Auth0 token)

```bash
# Get a token from Auth0 first, then:
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:3000/api/users/profile
```

## Common Issues and Solutions

### Issue: MongoDB Connection Failed

```bash
# Check if MongoDB is running
mongosh mongodb://localhost:27017

# If using Atlas, verify:
# 1. IP whitelist includes your IP
# 2. Database user has correct permissions
# 3. Connection string is correct
```

### Issue: Auth0 Authentication Failed

```bash
# Verify environment variables
echo $AUTH0_ISSUER_URL
echo $AUTH0_AUDIENCE

# Test Auth0 configuration
curl https://your-domain.auth0.com/.well-known/jwks.json
```

### Issue: Port Already in Use

```bash
# Find process using port 3000
lsof -i :3000

# Kill the process
kill -9 <PID>

# Or use a different port
PORT=3001 npm run start:dev
```

### Issue: Redis Connection Failed

This is optional - the app will fall back to in-memory caching:

```bash
# Install Redis locally
# macOS
brew install redis
redis-server

# Ubuntu
sudo apt-get install redis-server
sudo systemctl start redis
```

## Development Workflow

### 1. Code Structure

```
src/domains/yourDomain/
├── yourDomain.controller.ts    # HTTP endpoints
├── yourDomain.service.ts       # Business logic
├── yourDomain.schema.ts        # Database schema
├── yourDomain.interface.ts     # TypeScript types
└── yourDomain.module.ts        # NestJS module
```

### 2. Adding a New Endpoint

```typescript
// In controller
@Get('new-endpoint')
async newEndpoint(@Req() req: Request): Promise<any> {
  const user = req['user'];
  return this.service.handleNewEndpoint(user);
}

// In service
async handleNewEndpoint(user: User): Promise<any> {
  // Business logic here
  return { message: 'Success', user: user.name };
}
```

### 3. Database Operations

```typescript
// In service
async createRecord(data: CreateDto): Promise<Record> {
  const record = new this.recordModel(data);
  return record.save();
}

async findRecords(filter: any): Promise<Record[]> {
  return this.recordModel.find(filter).exec();
}
```

## Next Steps

1. **Explore the API**: Check out the full API documentation in the main README
2. **Run Tests**: `npm run test` to ensure everything works
3. **Check Examples**: Look at existing domains for patterns
4. **Read Architecture**: Review `docs/ARCHITECTURE.md` for deeper understanding
5. **Start Coding**: Begin implementing your features!

## Useful Commands

```bash
# Development
npm run start:dev          # Start with hot reload
npm run start:debug        # Start with debugging

# Testing
npm run test               # Run unit tests
npm run test:e2e          # Run integration tests
npm run test:cov          # Run with coverage

# Code Quality
npm run lint              # Check code style
npm run format            # Format code

# Building
npm run build             # Build for production
npm run start:prod        # Run production build
```

## Getting Help

- **Documentation**: Check the main README.md
- **Architecture**: Review docs/ARCHITECTURE.md
- **Issues**: Create a GitHub issue
- **Code Examples**: Look at existing domain implementations

Happy coding! 🚀
