# CoDDN API Architecture Guide

## Domain-Driven Design (DDD) Implementation

This document provides a detailed overview of the Domain-Driven Design architecture implemented in the CoDDN API.

## Core Architectural Principles

### 1. Domain Separation
Each business domain is isolated in its own module with clear boundaries:

```
src/domains/
├── alertZones/         # Geofenced monitoring areas
├── drones/             # Drone device management  
├── droneAuthorizations/# Permission and compliance
├── events/             # Event processing and history
├── dashboard/          # Analytics and reporting
├── users/              # User management
├── organizations/      # Multi-tenant support
└── ...
```

### 2. Layered Architecture
Each domain follows a consistent layered structure:

```
Domain/
├── controller.ts       # Presentation Layer (HTTP)
├── service.ts          # Application Layer (Business Logic)
├── schema.ts           # Infrastructure Layer (Data)
├── interface.ts        # Domain Layer (Contracts)
└── module.ts           # Dependency Injection
```

### 3. Dependency Injection
NestJS provides IoC container for clean dependency management:

```typescript
@Module({
  imports: [MongooseModule.forFeature([...])],
  controllers: [DomainController],
  providers: [DomainService],
  exports: [DomainService]
})
export class DomainModule {}
```

## Data Flow Patterns

### 1. Request Processing Flow
```
HTTP Request → Controller → Service → Repository → Database
                    ↓
Response ← Presenter ← Business Logic ← Data Access
```

### 2. Event-Driven Architecture
```
Kafka Events → Event Handlers → Business Services → Database
                    ↓
WebSocket ← Real-time Updates ← Event Aggregation
```

### 3. Caching Strategy
```
Request → Cache Check → Service Logic → Database
    ↓         ↓              ↓           ↓
Response ← Cache Hit ← Cache Miss ← Cache Update
```

## Key Design Patterns

### 1. Repository Pattern
Data access abstraction through Mongoose models:

```typescript
@Injectable()
export class DroneService {
  constructor(
    @InjectModel('drones') private droneModel: Model<Drone>
  ) {}
  
  async findAuthorizedDrones(orgId: string): Promise<Drone[]> {
    return this.droneModel.find({ org_id: orgId, isAuthorized: true });
  }
}
```

### 2. Service Layer Pattern
Business logic encapsulation:

```typescript
@Injectable()
export class AlertZoneService {
  async createAlertZone(data: CreateAlertZoneDto, user: User): Promise<AlertZone> {
    // Validation
    await this.validateServiceZones(data.serviceZones);
    
    // Business logic
    const alertZone = new this.alertZoneModel(data);
    alertZone.orgId = user.org_id;
    
    // Persistence
    return alertZone.save();
  }
}
```

### 3. Factory Pattern
Complex object creation:

```typescript
export class CacheFactory {
  static async createCacheManager(): Promise<CacheManager> {
    const stores = [];
    
    // Try Redis first
    try {
      const redisStore = createKeyv({ url: process.env.REDIS_HOST });
      stores.push(redisStore);
    } catch (error) {
      // Fallback to memory
      const memoryStore = new CacheableMemory({ ttl: '1h' });
      stores.push(memoryStore);
    }
    
    return { stores };
  }
}
```

## Security Architecture

### 1. Authentication Middleware
JWT validation for all protected routes:

```typescript
@Injectable()
export class AuthMiddleware implements NestMiddleware {
  async use(req: Request, res: Response, next: NextFunction) {
    const jwtCheck = auth({
      audience: process.env.AUTH0_AUDIENCE,
      issuerBaseURL: process.env.AUTH0_ISSUER_URL,
    });
    
    jwtCheck(req, res, (err) => {
      if (err) return res.status(401).json({ message: 'Unauthorized' });
      
      // Extract user context
      const token = req.headers.authorization.split(' ')[1];
      const decodedToken = jwt.decode(token);
      req['user'] = decodedToken;
      
      next();
    });
  }
}
```

### 2. Multi-Tenant Isolation
Organization-based data segregation:

```typescript
async findDrones(@Req() req: Request): Promise<Drone[]> {
  const orgId = req['org_id']; // From JWT token
  return this.droneService.findByOrganization(orgId);
}
```

## Performance Optimizations

### 1. Caching Layers
- **L1**: In-memory cache (fast, limited capacity)
- **L2**: Redis cache (distributed, persistent)
- **L3**: Database (source of truth)

### 2. Database Indexing
Strategic indexes for common query patterns:

```typescript
// Compound index for geospatial + organization queries
alertZoneSchema.index({ 
  "geometry": "2dsphere", 
  "orgId": 1, 
  "isActive": 1 
});

// TTL index for temporary data
eventSchema.index({ "createdAt": 1 }, { expireAfterSeconds: 2592000 });
```

### 3. Aggregation Pipelines
Optimized MongoDB aggregations for analytics:

```typescript
const pipeline = [
  { $match: { org_id: orgId, timestamp: { $gte: startDate } } },
  { $lookup: { from: 'event_profile', localField: 'event_id', foreignField: 'EVENT_ID' } },
  { $group: { _id: "$dateGrouped", eventCount: { $sum: 1 } } },
  { $sort: { '_id': 1 } }
];
```

## Scalability Considerations

### 1. Horizontal Scaling
- **Stateless services**: No server-side session storage
- **Database sharding**: Organization-based partitioning
- **Load balancing**: Multiple API instances

### 2. Microservice Readiness
- **Domain boundaries**: Clear service separation
- **API contracts**: Well-defined interfaces
- **Event messaging**: Kafka for inter-service communication

### 3. Resource Management
- **Connection pooling**: MongoDB connection reuse
- **Memory management**: Garbage collection optimization
- **CPU optimization**: Async/await patterns

## Monitoring and Observability

### 1. Structured Logging
```typescript
private readonly logger = new Logger(ServiceName.name);

async processEvent(eventData: EventData): Promise<void> {
  this.logger.log(`Processing event: ${eventData.id}`, {
    eventId: eventData.id,
    orgId: eventData.orgId,
    timestamp: new Date().toISOString()
  });
}
```

### 2. Health Checks
```typescript
@Get('health')
async healthCheck(): Promise<HealthStatus> {
  return {
    status: 'ok',
    database: await this.checkDatabaseConnection(),
    cache: await this.checkCacheConnection(),
    timestamp: new Date().toISOString()
  };
}
```

### 3. Metrics Collection
- **Response times**: API endpoint performance
- **Error rates**: Service reliability metrics
- **Resource usage**: Memory, CPU, database connections

## Testing Strategy

### 1. Unit Tests
Domain service testing with mocked dependencies:

```typescript
describe('DroneService', () => {
  let service: DroneService;
  let mockDroneModel: jest.Mocked<Model<Drone>>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        DroneService,
        { provide: getModelToken('drones'), useValue: mockDroneModel }
      ]
    }).compile();

    service = module.get<DroneService>(DroneService);
  });

  it('should create drone with authorization', async () => {
    // Test implementation
  });
});
```

### 2. Integration Tests
End-to-end API testing:

```typescript
describe('AlertZone API', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule]
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/api/alertZones (POST)', () => {
    return request(app.getHttpServer())
      .post('/api/alertZones')
      .send(alertZoneData)
      .expect(201);
  });
});
```

## Deployment Architecture

### 1. Container Strategy
```dockerfile
FROM node:20.9.0-alpine3.18
WORKDIR /usr/src/app
COPY package*.json ./
RUN npm install --frozen-lockfile
COPY . .
RUN npm run build
EXPOSE $PORT
CMD ["npm", "run", "start:prod"]
```

### 2. Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coddn-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: coddn-api
  template:
    spec:
      containers:
      - name: api
        image: coddn-api:latest
        ports:
        - containerPort: 3000
        env:
        - name: MONGODB_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: coddn-secrets
              key: mongodb-uri
```

This architecture provides a solid foundation for a scalable, maintainable, and secure drone management API following Domain-Driven Design principles.
