# Cache Configuration with Local Fallback

## Overview

The application now uses a multi-tier caching strategy that automatically falls back to local in-memory caching when Redis is not available.

## Configuration

### Cache Stores

The cache configuration in `src/app.module.ts` implements the following strategy:

1. **Primary Store (Redis)**: Attempts to connect to Redis using `@keyv/redis`
2. **Fallback Store (Memory)**: Always available using `CacheableMemory` from the `cacheable` package

### Connection Logic

```typescript
// Always add memory store as fallback first
const memoryStore = new CacheableMemory({
  ttl: '1h', // 1 hour default TTL
  lruSize: 1000, // LRU cache with max 1000 items
});
stores.push(memoryStore);

// Try to add Redis store with timeout
try {
  const redisStore = createKeyv({
    url: process.env.REDIS_HOST || "redis://localhost:6379",
  });
  
  // Test Redis connection with 5-second timeout
  const connectionTest = Promise.race([
    redisStore.get('test-connection'),
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Connection timeout')), 5000)
    )
  ]);
  
  await connectionTest;
  stores.unshift(redisStore); // Add Redis as primary store
  console.log('✅ Redis cache store connected successfully');
  console.log('🔄 Using multi-tier cache: Redis (primary) + Memory (fallback)');
} catch (error) {
  console.warn('⚠️ Redis cache store failed to connect:', error.message);
  console.log('🧠 Using in-memory cache store only');
}
```

## Behavior

### When Redis is Available
- **Primary Store**: Redis (distributed, persistent)
- **Fallback Store**: Memory (local, fast access)
- **Logging**: Shows "Using multi-tier cache: Redis (primary) + Memory (fallback)"

### When Redis is Not Available
- **Primary Store**: Memory (local, in-process)
- **Logging**: Shows "Using in-memory cache store only"
- **Graceful Degradation**: Application continues to function normally

## Memory Cache Configuration

The `CacheableMemory` store is configured with:

- **TTL**: 1 hour default (can be overridden per operation)
- **LRU Size**: 1000 items maximum
- **Features**: 
  - Automatic expiration
  - LRU eviction when size limit is reached
  - High performance in-memory operations

## Environment Variables

- `REDIS_HOST`: Redis connection URL (default: "redis://localhost:6379")

## Benefits

1. **Resilience**: Application starts and runs even when Redis is unavailable
2. **Performance**: Fast fallback to local memory cache
3. **Transparency**: Existing cache code continues to work without changes
4. **Monitoring**: Clear logging of cache store status
5. **Timeout Protection**: Redis connection attempts don't hang the application startup

## Usage

The cache can be used normally through the NestJS `CACHE_MANAGER` injection:

```typescript
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class MyService {
  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  async getData(key: string) {
    // This will use Redis if available, otherwise memory cache
    return await this.cacheManager.get(key);
  }

  async setData(key: string, value: any, ttl?: number) {
    // This will use Redis if available, otherwise memory cache
    return await this.cacheManager.set(key, value, ttl);
  }
}
```

## Testing

The configuration has been tested to ensure:
- ✅ Memory cache functionality (set, get, delete, has)
- ✅ Redis connection timeout handling
- ✅ Graceful fallback behavior
- ✅ Application startup with and without Redis
