---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coddn-api-qa
  namespace: coddn-qa
  labels:
    app: coddn-api-qa
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: coddn-api-qa
  template:
    metadata:
      labels:
        app: coddn-api-qa
    spec:
      containers:
        - name: coddn-api-qa
          image: 399444019738.dkr.ecr.us-east-2.amazonaws.com/qa/coddn-api:latest
          ports:
            - containerPort: 3002
          env:
            - name: PORT
              value: '3002'
            - name: RID_TOPIC
              value: RID_QA
