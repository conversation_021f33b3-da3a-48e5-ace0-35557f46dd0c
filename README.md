# CoDDN API

A comprehensive drone detection and management API built with NestJS, following Domain-Driven Design (DDD) principles. This REST API provides real-time drone monitoring, alert zone management, authorization tracking, and comprehensive analytics for drone operations.

## 🏗️ Architecture Overview

This application follows **Domain-Driven Design (DDD)** principles with **NestJS** framework, implementing a clean, modular architecture:

### Core Technologies

- **Framework**: NestJS (Node.js)
- **Database**: MongoDB with Mongoose ODM
- **Caching**: Redis/Valkey with in-memory fallback
- **Authentication**: Auth0 JWT-based authentication
- **Message Queue**: Apache Kafka (AWS MSK)
- **Real-time**: Socket.IO for WebSocket connections
- **Geospatial**: H3 hexagonal indexing system
- **External APIs**: AWS AppSync, FAA UAS data integration

### Domain Structure

The application is organized into distinct domains, each containing:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic and domain operations
- **Schemas**: MongoDB data models with Mongoose
- **Interfaces**: TypeScript type definitions
- **Modules**: NestJS module configuration

```
src/
├── domains/
│   ├── admin/              # Administrative operations
│   ├── alertNotifications/ # Real-time alert system
│   ├── alertZones/         # Geofenced alert areas
│   ├── dashboard/          # Analytics and reporting
│   ├── droneAuthorizations/# Drone permission management
│   ├── drones/             # Drone device management
│   ├── events/             # Event tracking and history
│   ├── logging/            # System audit logging
│   ├── organizations/      # Multi-tenant organization management
│   ├── serviceZone/        # Geographic service areas
│   ├── spectrumEvents/     # RF spectrum event processing
│   ├── systemNotifications/# System-wide notifications
│   ├── userPreferences/    # User configuration
│   └── users/              # User management
├── common/                 # Shared utilities and constants
├── database/               # Database connection modules
└── utils/                  # Helper services and utilities
```

## 🚀 Quick Start

### Prerequisites

- **Node.js** (v20.9.0 or higher)
- **npm** or **yarn** (Preferably Yarn)
- **MongoDB** (local or cloud instance)
- **Redis** (optional, falls back to in-memory cache)
- **Auth0** account for authentication
- **Python 3.11** (for live simulation scripts)

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd CoDDN_API
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   ```

3. **Install Python dependencies** (for Live Simulation)
   ```bash
   pip install -r ./pyScripts/requirements.txt
   ```

### Environment Configuration

Create environment files based on your deployment target:

#### Development (.env.dev)

```bash
# Server Configuration
ENV=dev
PORT=3000

# MongoDB Configuration
MONGODB_CONNECTION_STRING=mongodb://localhost:27017
DATABASE_NAME=coddn

# Spectrum Database (separate MongoDB instance)
SPECTRUM_MONGODB_CONNECTION_STRING=mongodb://localhost:27017
SPECTRUM_DATABASE_NAME=spectrum_events_db

# Auth0 Configuration
AUTH0_ISSUER_URL=https://your-auth0-domain.auth0.com
AUTH0_AUDIENCE=https://your-api-audience
AUTH0_CLIENT_ID=your-client-id
AUTH0_SECRET_KEY=your-secret-key

# Management API (for user management)
AUTH0_MANAGEMENT_API=https://your-auth0-domain.auth0.com
AUTH0_MANAGEMENT_API_CLIENT_ID=your-management-client-id
AUTH0_MANAGEMENT_API_CLIENT_SECRET=your-management-secret

# Redis Configuration (optional)
REDIS_HOST=redis://localhost:6379
ENABLE_CACHE=true

# AWS Configuration
AWS_ACCESS_KEY_ID=your-access-key
AWS_SERCRET_KEY_ID=your-secret-key
AWS_REGION=us-east-2

# Kafka Configuration
MSK_BROKERS=["broker1:9092","broker2:9092"]
GROUP_ID=devGroupApi
RID_UI_TOPIC=DETECTION_DEV
RID_TOPIC=RID_DEV

# AppSync Configuration
APP_SYNC_URL=https://your-appsync-endpoint/graphql
APP_SYNC_API_KEY=your-api-key

# Feature Flags
SPECTRUM_FLAG=false
```

#### Production (.env.prod)

Copy and modify the development configuration with production values.

### Database Setup

1. **MongoDB Setup**

   - Install MongoDB locally or use MongoDB Atlas
   - Create databases: `coddn` and `spectrum_events_db`
   - Ensure proper indexing for geospatial queries

2. **Redis Setup** (Optional)
   - Install Redis locally or use cloud Redis
   - The application will fall back to in-memory caching if Redis is unavailable

### Running the Application

```bash
# Development mode with hot reload
npm run start:dev
# or
yarn start:dev

# Production mode
npm run start:prod
# or
yarn start:prod

# Debug mode
npm run start:debug
# or
yarn start:debug
```

The API will be available at `http://localhost:3000` (or your configured PORT).

## 🔧 Development

### Code Structure and Patterns

#### Domain-Driven Design Implementation

Each domain follows a consistent structure:

```typescript
// Example: Alert Zones Domain
src/domains/alertZones/
├── alertZone.controller.ts    # HTTP endpoints
├── alertZone.service.ts       # Business logic
├── alertZone.schema.ts        # MongoDB schema
├── alertZone.interface.ts     # TypeScript interfaces
└── alertZone.module.ts        # NestJS module configuration
```

#### Service Layer Pattern

Services handle business logic and data access:

```typescript
@Injectable()
export class AlertZoneService {
  constructor(
    @InjectModel('alertZone') private alertZoneModel: Model<AlertZone>,
    private organizationService: OrganizationService,
    private loggingService: LoggingService,
  ) {}

  async createAlertZone(data: CreateAlertZoneDto, user: User): Promise<AlertZone> {
    // Business logic implementation
  }
}
```

#### Controller Pattern

Controllers handle HTTP requests and delegate to services:

```typescript
@Controller('api/alertZones')
export class AlertZoneController {
  constructor(private readonly alertZoneService: AlertZoneService) {}

  @Post()
  async createAlertZone(@Req() req: Request, @Body() dto: CreateAlertZoneDto) {
    const user = req['user'];
    return this.alertZoneService.createAlertZone(dto, user);
  }
}
```

#### MongoDB Schema Pattern

Schemas define data structure with Mongoose:

```typescript
const AlertZoneSchema = new Schema({
  name: { type: String, required: true },
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: false },
  orgId: { type: Schema.Types.ObjectId, ref: 'organizations', required: false },
  serviceZones: [{ type: Schema.Types.ObjectId, ref: 'servicezones' }],
  geometry: {
    type: { type: String, enum: ['Polygon', 'circle'], required: true },
    coordinates: { type: [[[Number]]], required: true },
  },
  latestStatus: { type: Number, enum: Object.values(AlertZoneStatusEnum), default: 1 },
  ...BaseSchemaFields, // Common fields like createdAt, updatedAt, isDeleted
});
```

### Testing

```bash
# Unit tests
npm run test
# or
yarn test

# End-to-end tests
npm run test:e2e
# or
yarn test:e2e

# Test coverage
npm run test:cov
# or
yarn test:cov

# Watch mode for development
npm run test:watch
# or
yarn test:watch
```

### Code Quality

```bash
# Linting
npm run lint
# or
yarn lint

# Code formatting
npm run format
# or
yarn format
```

## 📚 API Documentation

### Authentication

All API endpoints require JWT authentication via Auth0. Include the bearer token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Core Endpoints

#### Alert Zones

- `POST /api/alertZones` - Create personal alert zone
- `POST /api/alertZones/organization` - Create organization alert zone
- `GET /api/alertZones` - Find alert zones containing a point
- `PATCH /api/alertZones/:id/status` - Update alert zone status

#### Drones

- `GET /api/drones` - Get authorized drones for organization
- `POST /api/drones` - Register new drone
- `POST /api/drones/with-authorization` - Create drone with authorization
- `GET /api/drones/:id` - Get drone details
- `PUT /api/drones/:id` - Update drone information

#### Drone Authorizations

- `POST /api/drone-authorizations` - Create authorization
- `GET /api/drone-authorizations/drone/:droneId` - Get drone authorizations
- `PUT /api/drone-authorizations/:id` - Update authorization
- `DELETE /api/drone-authorizations/:id` - Revoke authorization

#### Dashboard Analytics

- `GET /api/dashboard/stats` - Get comprehensive dashboard statistics
  - Query parameters: `orgId`, `alertZoneIds[]`, `startDate`, `endDate`, `uasId[]`, `groupBy`
  - Returns: event counts, unique drones, average duration, time-series data

#### Events

- `GET /api/events/profile/:eventId` - Get event profile
- `GET /api/events/history` - Get event history with filtering
- `POST /api/events/profile` - Create event profile

### Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": { ... }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 🔧 Configuration

### Environment Variables Reference

| Variable                             | Description                            | Required | Default            |
| ------------------------------------ | -------------------------------------- | -------- | ------------------ |
| `ENV`                                | Environment name (dev/qa/staging/prod) | Yes      | -                  |
| `PORT`                               | Server port                            | No       | 3001               |
| `MONGODB_CONNECTION_STRING`          | Main MongoDB connection                | Yes      | -                  |
| `DATABASE_NAME`                      | Main database name                     | Yes      | coddn              |
| `SPECTRUM_MONGODB_CONNECTION_STRING` | Spectrum events MongoDB                | Yes      | -                  |
| `SPECTRUM_DATABASE_NAME`             | Spectrum database name                 | Yes      | spectrum_events_db |
| `AUTH0_ISSUER_URL`                   | Auth0 domain URL                       | Yes      | -                  |
| `AUTH0_AUDIENCE`                     | Auth0 API audience                     | Yes      | -                  |
| `AUTH0_CLIENT_ID`                    | Auth0 application client ID            | Yes      | -                  |
| `AUTH0_SECRET_KEY`                   | Auth0 application secret               | Yes      | -                  |
| `REDIS_HOST`                         | Redis connection string                | No       | -                  |
| `ENABLE_CACHE`                       | Enable/disable caching                 | No       | false              |
| `AWS_ACCESS_KEY_ID`                  | AWS access key                         | Yes      | -                  |
| `AWS_SERCRET_KEY_ID`                 | AWS secret key                         | Yes      | -                  |
| `AWS_REGION`                         | AWS region                             | Yes      | us-east-2          |
| `MSK_BROKERS`                        | Kafka broker list (JSON array)         | Yes      | -                  |
| `GROUP_ID`                           | Kafka consumer group ID                | Yes      | -                  |
| `APP_SYNC_URL`                       | AWS AppSync GraphQL endpoint           | Yes      | -                  |
| `APP_SYNC_API_KEY`                   | AppSync API key                        | Yes      | -                  |
| `SPECTRUM_FLAG`                      | Enable spectrum events processing      | No       | false              |

### Caching Strategy

The application implements a multi-tier caching strategy:

1. **Primary**: Redis/Valkey for distributed caching
2. **Fallback**: In-memory LRU cache (1000 items, 1-hour TTL)
3. **Graceful degradation**: Automatic fallback if Redis is unavailable

Cache keys are environment-prefixed to prevent conflicts:

- Development: `dev:user:sub:auth0|123`
- Production: `user:sub:auth0|123`

## 🚀 Deployment

### Docker Deployment

1. **Build Docker image**

   ```bash
   docker build --build-arg ENVIRONMENT=prod -t coddn-api .
   ```

2. **Run container**
   ```bash
   docker run -p 3000:3000 --env-file .env.prod coddn-api
   ```

### Kubernetes Deployment

Deployment files are available in the `deployment-files/` directory:

```bash
# Apply Kubernetes manifests
kubectl apply -f deployment-files/deployment-prod.yaml
kubectl apply -f eks-files/service-prod.yaml
kubectl apply -f eks-files/ingress.yaml
```

### AWS EKS Deployment

The application is designed for AWS EKS deployment with:

- **Load Balancer**: Application Load Balancer (ALB)
- **Ingress**: AWS Load Balancer Controller
- **Secrets**: AWS Secrets Manager integration
- **Monitoring**: CloudWatch logs and metrics

### Environment-Specific Builds

The Docker build process supports multiple environments:

```bash
# Development
docker build --build-arg ENVIRONMENT=dev -t coddn-api:dev .

# QA
docker build --build-arg ENVIRONMENT=qa -t coddn-api:qa .

# Staging
docker build --build-arg ENVIRONMENT=staging -t coddn-api:staging .

# Production
docker build --build-arg ENVIRONMENT=prod -t coddn-api:prod .
```

## 🏛️ Domain Details

### Core Business Domains

#### Alert Zones Domain

Manages geofenced areas for drone detection and monitoring:

- **Polygon and circular zones** with GeoJSON support
- **Multi-tenant support** (personal and organizational zones)
- **Real-time status tracking** (active/inactive)
- **H3 hexagonal indexing** for efficient spatial queries
- **Service zone integration** for regulatory compliance

#### Drone Management Domain

Comprehensive drone lifecycle management:

- **Device registration** with unique identifiers
- **Authorization tracking** with expiration dates
- **Multi-organizational support** with proper isolation
- **FAA UAS data integration** for regulatory compliance
- **Real-time status monitoring** and updates

#### Event Processing Domain

Real-time event ingestion and processing:

- **Event profiles** with drone identification data
- **Event history** with temporal tracking
- **Spectrum events** for RF-based detection
- **Kafka integration** for high-throughput processing
- **Analytics aggregation** for dashboard reporting

#### Dashboard Analytics Domain

Comprehensive reporting and analytics:

- **Time-series aggregation** (daily, weekly, monthly, yearly)
- **Multi-dimensional filtering** by zones, drones, time periods
- **Real-time metrics** with caching optimization
- **Geospatial analytics** with H3 integration
- **Export capabilities** for external reporting

### Data Flow Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Kafka/MSK     │───▶│   Event Ingestion │───▶│   MongoDB       │
│   (Real-time)   │    │   Processing      │    │   (Persistence) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Redis Cache   │◀───│   Business Logic  │───▶│   Auth0         │
│   (Performance) │    │   Services        │    │   (Security)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   WebSocket     │◀───│   REST API        │───▶│   External APIs │
│   (Real-time)   │    │   Controllers     │    │   (Integration) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔍 Monitoring and Observability

### Logging

The application uses structured logging with different levels:

- **Error**: System errors and exceptions
- **Warn**: Performance issues and degraded functionality
- **Info**: Business operations and state changes
- **Debug**: Detailed execution flow (development only)

### Health Checks

Built-in health check endpoints:

- `/health` - Basic application health
- `/health/database` - MongoDB connection status
- `/health/cache` - Redis connection status
- `/health/external` - External service dependencies

### Performance Monitoring

- **Response time tracking** for all API endpoints
- **Database query performance** monitoring
- **Cache hit/miss ratios** tracking
- **Memory usage** and garbage collection metrics

## 🔒 Security

### Authentication & Authorization

- **JWT-based authentication** via Auth0
- **Role-based access control** (RBAC)
- **Multi-tenant isolation** at the organization level
- **API rate limiting** and request throttling
- **CORS configuration** for cross-origin requests

### Data Protection

- **Input validation** and sanitization
- **SQL injection prevention** via Mongoose ODM
- **XSS protection** with proper output encoding
- **Sensitive data masking** in logs
- **Environment variable encryption** for secrets

### Network Security

- **HTTPS enforcement** in production
- **Security headers** (HSTS, CSP, etc.)
- **IP whitelisting** for administrative endpoints
- **VPC isolation** in AWS deployment

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Issues

```bash
# Check MongoDB connection
mongosh "your-connection-string"

# Verify database exists
use coddn
show collections
```

#### Redis Connection Issues

```bash
# Test Redis connectivity
redis-cli -u "your-redis-url" ping

# Check cache status
curl http://localhost:3000/health/cache
```

#### Auth0 Configuration Issues

```bash
# Verify JWT token
curl -H "Authorization: Bearer your-token" \
     http://localhost:3000/api/users/profile

# Check Auth0 configuration
echo $AUTH0_ISSUER_URL
echo $AUTH0_AUDIENCE
```

#### Performance Issues

```bash
# Monitor application metrics
npm run start:dev -- --inspect

# Check memory usage
node --max-old-space-size=4096 dist/main.js

# Database query optimization
# Enable MongoDB profiler for slow queries
```

### Debug Mode

Enable debug logging for troubleshooting:

```bash
# Set debug environment
export DEBUG=coddn:*

# Run with debug output
npm run start:debug
```

## 📈 Performance Optimization

### Caching Strategy

- **Multi-tier caching** (Redis + in-memory)
- **Cache warming** for frequently accessed data
- **TTL optimization** based on data volatility
- **Cache invalidation** on data updates

### Database Optimization

- **Compound indexes** for complex queries
- **Aggregation pipeline optimization** for analytics
- **Connection pooling** for high concurrency
- **Read replicas** for read-heavy workloads

### API Optimization

- **Response compression** (gzip)
- **Pagination** for large datasets
- **Field selection** to minimize payload size
- **Async processing** for heavy operations

## 🤝 Contributing

### Development Workflow

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Code Standards

- **TypeScript** strict mode enabled
- **ESLint** and **Prettier** for code formatting
- **Conventional commits** for commit messages
- **Unit tests** required for new features
- **Documentation** updates for API changes

### Testing Requirements

- **Unit tests**: Minimum 80% code coverage
- **Integration tests**: Critical business flows
- **E2E tests**: User journey validation
- **Performance tests**: Load and stress testing

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- **Documentation**: Check this README and inline code comments
- **Issues**: Create a GitHub issue for bugs and feature requests
- **Discussions**: Use GitHub Discussions for general questions
- **Email**: Contact the development team for urgent issues

## 🔄 Changelog

### Version 1.0.0

- Initial release with core drone management functionality
- Domain-driven architecture implementation
- Multi-tenant organization support
- Real-time event processing
- Comprehensive analytics dashboard
- Auth0 integration for authentication
- Redis caching with fallback strategy
- Docker and Kubernetes deployment support

---

**Built with ❤️ using NestJS, MongoDB, and Domain-Driven Design principles.**
