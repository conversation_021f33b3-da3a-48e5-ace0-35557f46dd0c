import datetime,time,threading,json
from optparse import OptionParser
from producer import Producer
import h3
import random,string,math
import os, logging, sys

RID_TOPIC = os.getenv("RID_TOPIC", "RID_QA")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    stream=sys.stdout,
    force=True
)
logger = logging.getLogger(__name__)

def generateGPS(loc,path,length,majurange):
	#loc is the center location where the drone fly to, we need to calculate pilot location using loc, distance and angle
	#path delivery will generate path: pilot location --> center location --> pilot location
	#length is the total gps iteration, use to control the step between each gps points, which equals to the tracking resolution
	if path.upper()=='DELIVERY':
		originLoc=loc.split(',')
		majurange=int(majurange)
		rc_range=int(majurange)+random.randint(50, 150)
		minurange=50
		angle= random.randint(-180, 180)
		#generate gps from center location to pilot location that is at distance=majurange and angle=angle, with the number of steps= majurange/length/2
		rc_loc=vinc_pt(float(originLoc[0]),float(originLoc[1]),int(angle),int(rc_range))
		list1=[vinc_pt(float(originLoc[0]),float(originLoc[1]),int(angle),int(d)) for d in range(minurange,majurange,int(majurange/length*2))]
		list2=list1[::-1]#generate a reverse list
		list2.extend(list1)#join two lists, the first and last gps of the list is the pilot location
		logger.info(list2)
		return rc_loc,list2
	else:
		return [],[]
	
def  vinc_pt(phi1, lamda1, alpha12, s ) : 
        """ 
        Returns the lat and long of projected point and reverse azimuth 
        given a reference point and a distance and azimuth to project. 
        lats, longs and azimuths are passed in decimal degrees 
        Returns ( phi2,  lamda2,  alpha21 ) as a tuple
        Parameters:
        ===========
            f: flattening of the ellipsoid
            a: radius of the ellipsoid, meteres
            phil: latitude of the start point, decimal degrees
            lamda1: longitude of the start point, decimal degrees
            alpha12: bearing, decimal degrees
            s: Distance to endpoint, meters
        """
        f=1/298.257223563
        a=6378137.0 

        
        piD4 = math.atan( 1.0 ) 
        two_pi = piD4 * 8.0 
    
        phi1    = phi1    * piD4 / 45.0 
        lamda1 = lamda1 * piD4 / 45.0 
        alpha12 = alpha12 * piD4 / 45.0 
        if ( alpha12 < 0.0 ) : 
            alpha12 = alpha12 + two_pi 
        if ( alpha12 > two_pi ) : 
            alpha12 = alpha12 - two_pi
        b = a * (1.0 - f) 
        TanU1 = (1-f) * math.tan(phi1) 
        U1 = math.atan( TanU1 ) 
        sigma1 = math.atan2( TanU1, math.cos(alpha12) ) 
        Sinalpha = math.cos(U1) * math.sin(alpha12) 
        cosalpha_sq = 1.0 - Sinalpha * Sinalpha 
        u2 = cosalpha_sq * (a * a - b * b ) / (b * b) 
        A = 1.0 + (u2 / 16384) * (4096 + u2 * (-768 + u2 * \
            (320 - 175 * u2) ) ) 
        B = (u2 / 1024) * (256 + u2 * (-128 + u2 * (74 - 47 * u2) ) ) 
        # Starting with the approx 
        sigma = (s / (b * A)) 
        last_sigma = 2.0 * sigma + 2.0   # something impossible 
        #print sigma   
        # Iterate the following 3 eqs unitl no sig change in sigma 
        # two_sigma_m , delta_sigma 
        while ( abs( (last_sigma - sigma) / (sigma+1.0e-9)) > 1.0e-9 ):
            two_sigma_m = 2 * sigma1 + sigma 
            delta_sigma = B * math.sin(sigma) * ( math.cos(two_sigma_m) \
                    + (B/4) * (math.cos(sigma) * \
                    (-1 + 2 * math.pow( math.cos(two_sigma_m), 2 ) -  \
                    (B/6) * math.cos(two_sigma_m) * \
                    (-3 + 4 * math.pow(math.sin(sigma), 2 )) *  \
                    (-3 + 4 * math.pow( math.cos (two_sigma_m), 2 )))))
            last_sigma = sigma 
            sigma = (s / (b * A)) + delta_sigma 
        phi2 = math.atan2 ( (math.sin(U1) * math.cos(sigma) +\
            math.cos(U1) * math.sin(sigma) * math.cos(alpha12) ), \
            ((1-f) * math.sqrt( math.pow(Sinalpha, 2) +  \
            pow(math.sin(U1) * math.sin(sigma) - math.cos(U1) * \
            math.cos(sigma) * math.cos(alpha12), 2))))
        lamda = math.atan2( (math.sin(sigma) * math.sin(alpha12 )),\
            (math.cos(U1) * math.cos(sigma) -  \
            math.sin(U1) *  math.sin(sigma) * math.cos(alpha12))) 
        C = (f/16) * cosalpha_sq * (4 + f * (4 - 3 * cosalpha_sq )) 
        omega = lamda - (1-C) * f * Sinalpha *  \
            (sigma + C * math.sin(sigma) * (math.cos(two_sigma_m) + \
            C * math.cos(sigma) * (-1 + 2 *\
            math.pow(math.cos(two_sigma_m), 2) ))) 
        lamda2 = lamda1 + omega 
        alpha21 = math.atan2 ( Sinalpha, (-math.sin(U1) * \
            math.sin(sigma) +
            math.cos(U1) * math.cos(sigma) * math.cos(alpha12))) 
        alpha21 = alpha21 + two_pi / 2.0 
        if ( alpha21 < 0.0 ) : 
            alpha21 = alpha21 + two_pi 
        if ( alpha21 > two_pi ) : 
            alpha21 = alpha21 - two_pi 
        phi2 = phi2 * 45.0 / piD4 
        lamda2 = lamda2 * 45.0 / piD4 
        alpha21 = alpha21 * 45.0 / piD4
        #return phi2, lamda2, alpha21 
        return (phi2,lamda2)

def vinc_dist(real,calc):
    logger.info(vincenty(real,calc).meters)

def dict2jsonString(CMD,NODE_ID,MODULE_TYPE,INFO):
	INFO['CMD']=CMD
	INFO['NODE_ID']=NODE_ID
	INFO['MODULE_TYPE']=MODULE_TYPE
	if 'TIME_STAMP' not in INFO:
		INFO['TIME_STAMP']=datetime.datetime.now().astimezone().isoformat()
	return json.dumps(INFO)

class RID_Sim(threading.Thread):
	def __init__(self,options,args):
		threading.Thread.__init__(self)
		self.center=str(options.lat)+','+str(options.lng)
		self.altitude=options.altitude
		self.speed=options.speed
		self.rssi_min=options.RSSI
		self.rssi_max=options.rssi
		self.length=options.length
		self.path=options.path
		self.sNum=options.sensors
		self.area=options.area
		self.zone=options.zone
		self.delay=options.delay
		self.range=options.range
		self.args=args
		self.rid_sim_data = []
		self.sensor_list = ['test_1','test_2','test_3']#need to retrive the sensor registered under a service zone
		self.Push = Producer()

	# initialize RID simulation data
	def initRID(self,rc_loc,first_gps):
		self.rid_sim_data = [
			"Sim_RID_"+"".join(random.choices(string.ascii_uppercase +
                             string.digits, k=6)), # "DEVICE_ID":
			# "Sim_RID_UYGH0D", # "DEVICE_ID":
			"", # "EVENT_ID": 
			h3.latlng_to_cell(first_gps[0], first_gps[1], 5),#h3 index
			first_gps[0],#lat
			first_gps[1],#lon
			self.altitude,#altitude
			self.speed,#speed
			{
			"radio_type" : "wlan", 
			"frame_time": "Aug 06, 2024 19:00:34.115516 UTC", 
			"radio_channel" : "6", 
			"radio_frequency" : "2437", 
			"radio_rss" : "-62", 
			"radio_bssid" : "00:0e:8e:bf:0e:69", 
			"radio_ssid" : "DroneIDTest",
			"ODID_basicID_id": "1581F5FJC247600D8E24", 
			"ODID_basicID_uaType": "2", 
			"ODID_loc_status": "2", 
			"ODID_loc_direction": "361.000000", 
			"ODID_loc_speed": "0", 
			"ODID_loc_vspeed": "0", 
			"ODID_loc_lat" : first_gps[0], 
			"ODID_loc_lon" : first_gps[1], 
			"ODID_loc_geoAlt" : "110", 
			"ODID_loc_height" : "80", 
			"ODID_operator_id": "FIN87astrdge12k8", 
			"ODID_operator_type": "0", 
			"ODID_system_lat" : rc_loc[0], 
			"ODID_system_lon" : rc_loc[1], 
			"ODID_system_geoAlt" : "20.500000", 
			"ODID_selfID_type": "0" , 
			"ODID_selfID_id" : "Drone ID test flight---"
			},
			'''
			{
				"frame_time": '', #"Jan 5, 2023 17:46:46.044065795 UTC",
				"wlan_radio_channel": '7',
				"wlan_radio_frequency": '2442',
				"wlan_radio_signal_dbm": '-67',
				"wlan_bssid": "d2:b8:d7:4d:1b:84",
				"wlan_ssid": "RID_34b4724dfa4c",
				"opendroneid": "Open Drone ID - Operator ID Message (5)",
				"OpenDroneID_basicID_id_asc": "N950MA",
				"OpenDroneID_basicID_uaType": '2',
				"OpenDroneID_loc_status": '2',
				"OpenDroneID_loc_direction": '167',
				"OpenDroneID_loc_speed": self.speed, #76,
				"OpenDroneID_loc_vspeed": '5',
				"OpenDroneID_loc_lat": first_gps[0], # generate GPS
				"OpenDroneID_loc_lon": first_gps[1], # generate GPS
				"OpenDroneID_loc_geoAlt": '2637',
				"OpenDroneID_loc_height": '6199',
				"OpenDroneID_operator_id": "UFO_Area_51\\u0011\\u0012@$",
				"OpenDroneID_operator_type": '0',
				'OpenDroneID_system_lat': first_gps[0],
				'OpenDroneID_system_lon': first_gps[1],
			},
			'''
		]

	# update coordinates for RID simulation
	def updateRID(self,lat,lon):
		self.rid_sim_data[3]=lat
		self.rid_sim_data[4]=lon
		self.rid_sim_data[5]=self.altitude+random.randint(-50, 50)
		self.rid_sim_data[6]=self.speed+random.randint(-10, 10)
		self.rid_sim_data[7]["OpenDroneID_loc_lat"]=lat
		self.rid_sim_data[7]["OpenDroneID_loc_lon"]=lon

	
	# activate simulation
	def run(self):
		#print(self.sensor_list)
		print("STDOUT TEST: This should go to stdout", file=sys.stdout, flush=True)
		print("STDERR TEST: This should go to stderr", file=sys.stderr, flush=True)
		logger.info("\n--- Simulation Started ---")
		logger.info(f"Using Kafka Topic: {RID_TOPIC}")

		rc_loc, gps_list = generateGPS(self.center,self.path,self.length,self.range)
		logger.info(f"Generated {len(gps_list)} GPS points.")

		if not gps_list:
			logger.error("ERROR: No GPS path was generated. Aborting simulation.")
			return

		self.initRID(rc_loc,gps_list[0])
		
		for [lat,lon] in gps_list:
			for cID in self.sensor_list:
				self.updateRID(lat,lon)
				#Push = sim.Pusher(self.addr,self.message_mode,self.cID_list[0])
				########Modify here to add Kafaka producer'###########
				#CMD='DETECTION',MODULE_TYPE='RID', INFO=[self.rid_sim_data], cID=cID
				# self.Push.produce_message(cID, str([self.rid_sim_data]))
				dict_data = dict(zip(['DEVICE_ID','EVENT_ID','H3_INDEX','LAT','LON','ALTITUDE','SPEED','INFO'],self.rid_sim_data))
				json_data_str = dict2jsonString('RID',cID,'RID',dict_data)
				
				# --- START OF NEW DIAGNOSTIC CODE ---
				logger.info("\n--- Pre-check: Payload for Kafka ---")
				logger.info(f"Data to be sent (string): {json_data_str}")
				
				original_json_is_valid = False
				try:
					original_json_data = json.loads(json_data_str)
					original_json_is_valid = True
					logger.info("LOG: Original JSON is VALID.")
					
					# Test 1: Check for known data issues in the original payload
					frame_time = original_json_data.get('INFO', {}).get('frame_time')
					if frame_time and str(datetime.datetime.now().year) not in frame_time:
						logger.warning("WARNING: 'frame_time' is outdated.")

				except json.JSONDecodeError as e:
					logger.error(f"CRITICAL ERROR: Original JSON is INVALID. Error: {e}")
				
				# Test 2: Generate and test a new, corrected format
				logger.info("\n--- Test 2: Checking New Frontend Format ---")
				new_format_is_valid = False
				try:
					# Create a new, temporary payload with a corrected format
					new_format_data = {
						"COMPLETE": "0",
						"DEVICE_ID": self.rid_sim_data[0],
						"EVENT_ID": self.rid_sim_data[1],
						"H3_LIST": [self.rid_sim_data[2]],
						"INFO": {
							"START_TIME": datetime.datetime.now().isoformat(),
							"END_TIME": "",
							"DURATION": ""
						},
						"START_TIME": datetime.datetime.now().isoformat(),
						"UAS_ID": self.rid_sim_data[7].get("ODID_basicID_id"),
						"_id": ""
					}
					new_json_str = json.dumps(new_format_data)
					# Test if the newly created string is valid
					json.loads(new_json_str) 
					new_format_is_valid = True
					logger.info("LOG: New format creation was SUCCESSFUL.")
					logger.debug(f"DEBUG: New format payload looks like: {new_json_str}")
				except Exception as e:
					logger.error(f"ERROR: Failed to create or validate new format. Error: {e}")
				
				logger.info("------------------------------\n")
				
				# The original code logic follows, unmodified
				if original_json_is_valid:
					try:
						self.Push.produce_message(RID_TOPIC,json_data_str,original_json_data['DEVICE_ID'])
						logger.info("LOG: Original message production call was SUCCESSFUL.")
					except Exception as e:
						logger.error(f"CRITICAL ERROR: Failed to produce original message to Kafka. Error: {e}")
				
				logger.info(f"message sent at {datetime.datetime.now()} ----- {self.rid_sim_data}")
				######################################################
			time.sleep(1)


if __name__ == '__main__':
	usage="%prog: [options] sensor_list"
	parser=OptionParser(usage=usage)
	parser.add_option("-A", "--lat", type="string", default='40.3168039',help="Center GPS start point[default=%default]")
	parser.add_option("-O", "--lng", type="string", default='-74.0368967',help="Center GPS start point[default=%default]") #35.448735,-77.607615
	parser.add_option("-f", "--file", type="string", default='',help="Specify data file[default=%default]")
	parser.add_option("-a", "--area", type="string", default='Giants',help="Specify Giants or Paddock[default=%default]")
	parser.add_option("-z", "--zone", type="int", default=0,help="Specify sub area. 0,1,2 depending on area used[default=%default]")
	parser.add_option("-s", "--sensors", type="int", default=1,help="How many sensors are detecting device[default=%default]")
	parser.add_option("-R", "--RSSI", type="float", default=-65,help="set starting power level[default=%default]")
	parser.add_option("-r", "--rssi", type="float", default=-55,help="set max power level[default=%default]")
	parser.add_option("-S", "--speed", type="float", default=25,help="speed affects the scaling of movement (0,100) Low for controllers, high for drones.[default=%default]")
	parser.add_option("-t", "--altitude", type="float", default=55,help="Drone altitude.[default=%default]")
	parser.add_option("-P", "--path", type="string", default='delivery',help="path affects the pattern in flight path. Options are mixed, horizontal, verticle, delivery, random, circle [default=%default]")
	parser.add_option("-N", "--number", type="int", default=2,help="number of incidents at the same time[default=%default]")
	parser.add_option("-T", "--type", type="string", default="mixed",help="types of signals report. Options are video, controller,WF, unknown, mixed.[default=%default]")
	parser.add_option("-d", "--delay", type="int", default=3,help="how long between starts of new incidents[default=%default]")
	parser.add_option("-L", "--length", type="int", default=15,help="number of entries for the incident[default=%default]")
	parser.add_option("-l", "--range", type="int", default=1200,help="range of where the flight starts[default=%default]")
	(options, args)=parser.parse_args()
	rid = RID_Sim(options,args)
	rid.start()


