import os,logging, sys
import json
import time
import datetime
import h3
from threading import Thread, Event
import argparse

# TODO: import your Producer class
from producer import Producer

HEARTBEAT_TOPIC = os.getenv("HEARTBEAT_TOPIC", "HEARTBEAT_DEV")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    stream=sys.stdout,
    force=True
)
logger = logging.getLogger(__name__)

class HeartbeatGenerator:
    """
    Simple heartbeat generator.

    Inputs:
      - STATUS: 0=offline, 1=online, 2=error
      - LAT, LON, ALTITUDE

    Behavior:
      - Computes H3_INDEX from LAT/LON at h3_level.
      - Can send on a timer (start/stop) OR one-off via send_once().
    """

    def __init__(
        self,
        lat: float,
        lon: float,
        altitude: float = 0.0,
        *,
        status: int = 1,
        send_interval_sec: float = 30.0,
        run_duration_sec: float = 300.0,
        h3_level: int = 5,
        server_type: str = "prod",
        code_version: str = "rid_prod_v2.3.10",
    ):
        """
        Args:
            status: 0=offline, 1=online, 2=error
            lat: Latitude (deg)
            lon: Longitude (deg)
            altitude: Altitude (m)
            send_interval_sec: Seconds between heartbeats (used by start/stop loop)
            run_duration_sec: Total seconds to run before auto-stopping (used by start/stop loop)
            h3_level: H3 resolution
            server_type: Added as TOKEN_ENV
            code_version: Added as CODE_VERSION
        """
        if send_interval_sec <= 0:
            raise ValueError("send_interval_sec must be > 0")
        if run_duration_sec <= 0:
            raise ValueError("run_duration_sec must be > 0")
        if status not in (0, 1, 2):
            raise ValueError("status must be one of {0, 1, 2}")

        self.status = int(status)         # 0=offline, 1=online, 2=error
        self.lat = float(lat)
        self.lon = float(lon)
        self.altitude = float(altitude)

        self.interval = float(send_interval_sec)
        self.duration = float(run_duration_sec)
        self.h3_level = int(h3_level)
        self.server_type = str(server_type)
        self.code_version = str(code_version)

        self._stop_event = Event()
        self._thread: Thread | None = None

    def start(self):
        """Start periodic sending in a background thread (uses send_interval_sec & run_duration_sec)."""
        if self._thread and self._thread.is_alive():
            return
        self._stop_event.clear()
        self._thread = Thread(target=self._run_loop, name="HeartbeatGenerator", daemon=True)
        self._thread.start()

    def stop(self):
        """Stop sending."""
        self._stop_event.set()
        if self._thread:
            self._thread.join()

    def _run_loop(self):
        start = time.monotonic()
        next_send = start
        while not self._stop_event.is_set():
            if (time.monotonic() - start) >= self.duration:
                break

            try:
                self._send_once()
            except Exception as e:
                logger.error(f"[HeartbeatGenerator] Error sending heartbeat: {e}")

            next_send += self.interval
            wait = max(0.0, next_send - time.monotonic())
            self._stop_event.wait(wait)

    def send_once(self) -> str:
        """
        Build and send one heartbeat now (synchronous).
        Returns the JSON string that was produced and sent.
        """
        return self._send_once()

    def _send_once(self) -> str:
        """Build and send one heartbeat payload; returns JSON string."""
        try:
            h3_index = h3.latlng_to_cell(self.lat, self.lon, self.h3_level)
        except Exception:
            h3_index = None

        heartbeat = {
            "STATUS": self.status,  # 0=offline, 1=online, 2=error
            "LAT": self.lat,
            "LON": self.lon,
            "ALTITUDE": self.altitude,
            "H3_INDEX": h3_index,
            "H3_LEVEL": self.h3_level,
            "CODE_VERSION": self.code_version,
            "TOKEN_ENV": self.server_type,
            "CPU": 0.0,
            "MEM": 0.0,
            "CPU_TEMP": 0.0,
            "IP": "0.0.0.0",
            "HOSTNAME": "unknown",
            "TEMP": 0.0,
            "STORAGE": 0,
            "STORAGE_USAGE": 0.0,
            "MISSED_NODES": [],
        }

        wrapped = self.coddn_heartbeat_data_wrapper(heartbeat)
        json_str = json.dumps(wrapped)

        # Send to Kafka (Producer must be configured)
        self.send_data(HEARTBEAT_TOPIC, json_str, wrapped["NODE_ID"])

        # Console trace
        logger.info(f"[HeartbeatGenerator] Sent heartbeat (INFO): {json.dumps(heartbeat)}")
        return json_str

    def coddn_heartbeat_data_wrapper(self, item: dict) -> dict:
        report_data = {}
        report_data['NODE_ID'] = self.getnodeID()
        report_data['TIME_STAMP'] = datetime.datetime.now().astimezone().isoformat()
        report_data['INFO'] = item
        return report_data

    def getnodeID(self) -> str:
        out = str(os.popen("ifconfig | grep ether|head -n 1| tr -d :|awk '{print $2}'").read()).replace('\n','')
        return f"sim_{out}" if out else "sim_unknown"

    def send_data(self, topic: str, data: str, partition_key: str):
        """Send data to the given topic using Producer."""
        try:
            producer = Producer()
            producer.produce_message(topic, data, partition_key)
        except Exception as e:
            logger.error(f"CRITICAL ERROR: Failed to produce to Kafka. Error: {e}")

def main():
    """
    CLI entry that mirrors the style of your run() example:
      - Prints a banner
      - Shows the Kafka topic
      - Sends EXACTLY `length` heartbeats at `time_gap` seconds apart
      - Validates/prints JSON before producing, and logs results
    """
    parser = argparse.ArgumentParser(description="Heartbeat Generator CLI")
    parser.add_argument("-l", "--lat", type=float, required=True, help="Latitude")
    parser.add_argument("-o", "--lon", type=float, required=True, help="Longitude")
    parser.add_argument("-n", "--length", type=int, required=True, help="Total number of heartbeats to send")
    parser.add_argument("-g", "--time_gap", type=float, required=True, help="Seconds between heartbeats")
    parser.add_argument("-s", "--status", type=int, choices=[0, 1, 2], required=True, help="0=offline, 1=online, 2=error")
    parser.add_argument("-a", "--altitude", type=float, default=0.0, help="Altitude meters (default=0.0)")
    parser.add_argument("-H", "--h3_level", type=int, default=5, help="H3 resolution level (default=5)")
    parser.add_argument("-t", "--server_type", type=str, default="cli", help="Server environment token (default=cli)")
    parser.add_argument("-c", "--code_version", type=str, default="cli_test_v1.0", help="Code version string")
    args = parser.parse_args()

    logger.info(f"Using Kafka Topic: {HEARTBEAT_TOPIC}")

    total = args.length
    if total <= 0:
        logger.error("ERROR: --length must be > 0. Aborting.")
        return
    if args.time_gap <= 0:
        logger.error("ERROR: --time_gap must be > 0. Aborting.")
        return

    logger.info(f"Configured to send {total} heartbeat(s).")
    hb = HeartbeatGenerator(
        lat=args.lat,
        lon=args.lon,
        altitude=args.altitude,
        status=args.status,
        send_interval_sec=args.time_gap,
        run_duration_sec=args.length * args.time_gap,
        h3_level=args.h3_level,
        server_type=args.server_type,
        code_version=args.code_version,
    )

    logger.info(f"Initial location -> LAT: {args.lat}, LON: {args.lon}, ALT: {args.altitude}")
    logger.info("STATUS mapping: 0=offline, 1=online, 2=error")

    for i in range(total):
        json_data_str = hb.send_once()
        
        try:
            original_json_data = json.loads(json_data_str)

            info = original_json_data.get("INFO", {})
            ts = original_json_data.get("TIME_STAMP")
            if not ts:
                logger.warning("WARNING: Missing TIME_STAMP in wrapped payload.")
            if "H3_INDEX" in info and info["H3_INDEX"] is None:
                logger.warning("WARNING: H3_INDEX is None (lat/lon may be invalid or h3 failed).")
        except json.JSONDecodeError as e:
            logger.error(f"CRITICAL ERROR: JSON is INVALID. Error: {e}")

        if i < total - 1:
            time.sleep(args.time_gap)

if __name__ == "__main__":
    main()