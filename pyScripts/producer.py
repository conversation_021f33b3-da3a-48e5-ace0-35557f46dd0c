from confluent_kafka import Producer
import socket
from aws_msk_iam_sasl_signer import MSKAuthTokenProvider
from botocore.credentials import CredentialProvider, Credentials
import os
import logging

ACCESS_KEY = os.getenv("AWS_ACCESS_KEY_ID","********************")
SECRET_KEY = os.getenv("AWS_SERCRET_KEY_ID","XB5g7U5NBtDCzd5vTXoJLumcDEuzA8sOulVO2tIg")
REGION = os.getenv("AWS_REGION", "us-east-2")
BOOTSTRAP_SERVERS = os.getenv("MSK_PYTHON_BROKERS","b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198")

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

class TestCredentialProvider(CredentialProvider):
    __test__ = False

    def load(self):
        return Credentials(
            access_key= ACCESS_KEY, 
            secret_key= SECRET_KEY
        )

def oauth_cb(oauth_config):
	auth_token, expiry_ms = MSKAuthTokenProvider.generate_auth_token_from_credentials_provider(REGION, TestCredentialProvider())
	#logger.info(auth_token)
	# Note that this library expects oauth_cb to return expiry time in seconds since epoch, while the token generator returns expiry in ms
	return auth_token, expiry_ms/1000

class Producer():
    base_conf = {
    "bootstrap.servers": BOOTSTRAP_SERVERS,
    "security.protocol": "SASL_SSL",
    "client.id": socket.gethostname(),
    "queue.buffering.max.ms": 10000,
    }

    if os.getenv("CONFLUENT_CONFIG", "false") == "true":
        auth_conf = {
            "sasl.mechanism": "PLAIN",
            "sasl.username": ACCESS_KEY,
            "sasl.password": SECRET_KEY,
        }
    else:
        auth_conf = {
            "sasl.mechanism": "OAUTHBEARER",
            "oauth_cb": oauth_cb,
        }
    conf = {**base_conf, **auth_conf}
    producer = Producer(conf)
    
    def produce_message(self, topic, message, partition_key):
        key_bytes = partition_key.encode() if partition_key else None
        try:
            self.producer.produce(
                topic=topic,
                key=key_bytes,
                value=message.encode(),
                callback=self.delivery_report
            )
            self.producer.poll(0)  # Trigger delivery callbacks
            self.producer.flush()  # Wait for messages to be delivered
            logger.info(f"Produced! {topic} {message}")
        except:
            logger.error(f"Error producing message to topic {topic}")
            raise

    def delivery_report(self, err, msg):
        if err is not None:
            logger.error(f"Message delivery failed: {err}")
        else:
            logger.info(f"Message delivered to {msg.topic()} [{msg.partition()}]")