import { Schema } from 'mongoose';
import { BaseSchemaFields } from 'src/common/base.schema';
import Constants from 'src/common/constants';

const DroneAuthorizationSchema = new Schema({
  drone_id: { type: Schema.Types.ObjectId, ref: Constants.drones, required: true },
  org_id: { type: String, required: true },
  is_authorized: { type: Boolean, default: true },
  alert_zones: [{ type: Schema.Types.ObjectId, ref: Constants.alertZones }],
  authorize_expires_at: { type: Date, required: false, default: null },
  authorized_by: { type: String, required: true },
  notes: { type: String, default: '' },
   ...BaseSchemaFields
});

DroneAuthorizationSchema.index({ drone_id: 1, createdAt: -1, is_authorized: 1 });

export default DroneAuthorizationSchema;
