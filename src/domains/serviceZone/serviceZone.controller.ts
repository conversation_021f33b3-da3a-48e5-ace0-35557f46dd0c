import { Controller, Get, Req, Post, Body, Param, Delete, Put, Query } from '@nestjs/common';
import { ServiceZoneService } from './serviceZone.service';

@Controller('api/serviceZones')
export class ServiceZonesController {
  constructor(private readonly serviceZonesService: ServiceZoneService) {}

  @Get('/user')
  getSubscribedServiceZones(@Req() req: Request) {
    const user = req['user'];
    return this.serviceZonesService.getSubscribedServiceZonesForUser(user.org_id, user);
  }

  @Get()
  getAllServiceZones(@Query('lat') lat: number, @Query('lng') lng: number, @Query('name') name: string) {
    return this.serviceZonesService.getAllServiceZones(lat, lng, name);
  }

  @Put(':id')
  updateServiceZoneById(@Param('id') id: string, @Body() serviceZone: any) {
    return this.serviceZonesService.updateServiceZoneById(id, serviceZone);
  }

  @Post()
  createServiceZone(@Body() serviceZone: any) {
    return this.serviceZonesService.createServiceZone(serviceZone);
  }


  @Post("gps")
  createServiceZoneWithGps(@Body() serviceZone: any) {
    return this.serviceZonesService.createServiceZoneWithGPS(serviceZone);
  }

  @Get(':id')
  getServiceZoneById(@Param('id') id: string) {
    return this.serviceZonesService.getServiceZoneById(id);
  }

  @Delete(':id')
  deleteServiceZoneById(@Param('id') id: string) {
    return this.serviceZonesService.deleteServiceZoneById(id);
  }
}
