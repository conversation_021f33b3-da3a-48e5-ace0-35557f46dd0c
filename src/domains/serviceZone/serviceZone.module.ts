import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import ServiceZoneSchema from './serviceZone.schema';
import { ServiceZoneService } from './serviceZone.service';
import { OrganizationModule } from '../organizations/organization.module';
import { User, UserSchema } from '../users/user.schema';
import { H3Service } from 'src/utils/h3.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'servicezones', schema: ServiceZoneSchema }]),
    OrganizationModule,
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
  ],
  providers: [ServiceZoneService, H3Service],
  exports: [ServiceZoneService],
})
export class ServiceZoneModule {}
