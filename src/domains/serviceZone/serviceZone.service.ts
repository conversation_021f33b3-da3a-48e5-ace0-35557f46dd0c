import { BadRequestException, Injectable, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { OrganizationService } from '../organizations/organization.service';
import { User } from '../users/user.schema';
import { H3Service } from 'src/utils/h3.service';
import { CacheKeyPatterns } from '../../utils/cache.utils';

@Injectable()
export class ServiceZoneService {
  constructor(
    private h3Service: H3Service,
    @InjectModel('servicezones') private serviceZonesModel: Model<any>,
    private organizationService: OrganizationService,
    @InjectModel(User.name) private userModel: Model<User>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) { }

  async createServiceZone(serviceZone: any) {
    try {
      const createdServiceZone = await this.serviceZonesModel.create(serviceZone);

      // Invalidate related H3 cache entries
      if (createdServiceZone.h3_indexes) {
        await this.invalidateH3Cache(createdServiceZone.h3_indexes);
      }

      return createdServiceZone;
    } catch (error) {
      if (error.code === 11000) {
        throw new BadRequestException('Service zone already exists');
      }
      console.error('Error creating service zone:', error);
      throw new BadRequestException('Failed to create service zone');
    }
  }

  async createServiceZoneWithGPS(serviceZoneReq: any) {
    try {
      const current_h3_index = this.h3Service.getH3Index(serviceZoneReq.gps.lat, serviceZoneReq.gps.lng, 5);
      const neighbour_h3_indexes = this.h3Service.getNeighbors(current_h3_index);
      const oldServiceZone = await this.findServiceZonesFromH3Indexes([current_h3_index]);
      if (oldServiceZone.length > 0) {
        throw new BadRequestException('Service zone already exists');
      }

      const serviceZone = {
        name: serviceZoneReq.name,
        h3_indexes: [current_h3_index, ...neighbour_h3_indexes],
      };

      const createdServiceZone = await this.serviceZonesModel.create(serviceZone);

      // Invalidate related H3 cache entries
      if (createdServiceZone.h3_indexes) {
        await this.invalidateH3Cache(createdServiceZone.h3_indexes);
      }

      return createdServiceZone;
    } catch (error) {
      if (error.code === 11000) {
        throw new BadRequestException('Service zone already exists');
      }
      console.error('Error creating service zone:', error);
      throw new BadRequestException(error);
    }
  }

  async getServiceZoneById(id: string) {
    try {
      const cacheKey = CacheKeyPatterns.SERVICE_ZONE_BY_ID(id);

      // Try to get from cache first
      const cachedServiceZone = process.env.ENABLE_CACHE === 'true'? await this.cacheManager.get<any>(cacheKey) : undefined;
      if (cachedServiceZone && process.env.ENABLE_CACHE === 'true') {
        return cachedServiceZone;
      }

      // If not in cache, query database
      const serviceZone = await this.serviceZonesModel.findById(id);

      // Store in cache with 10 minute TTL (600 seconds)
      if (serviceZone && process.env.ENABLE_CACHE === 'true') {
        await this.cacheManager.set(cacheKey, serviceZone, 600000); // 10 minutes in milliseconds
      }

      return serviceZone;
    } catch (error) {
      console.error('Error finding service zone:', error);
      return [];
    }
  }

  async getAllServiceZones(lat, lng, name: string) {
    try {
      if (lat && lng) {
        const current_h3_index = this.h3Service.getH3Index(lat, lng, 5);
        const serviceZones = await this.findServiceZonesFromH3Indexes([current_h3_index]);
        for (let i = 0; i < serviceZones.length; i++) {
          serviceZones[i].url = this.buildUrl(serviceZones[i].h3_indexes)
        }

        return serviceZones;
      } else if (name) {
        const serviceZones = await this.serviceZonesModel.find({ name });

        for (let i = 0; i < serviceZones.length; i++) {
          serviceZones[i].url = this.buildUrl(serviceZones[i].h3_indexes)
        }
        return serviceZones;
      }
      else {

        const serviceZones = await this.serviceZonesModel.find({}, { _id: 1, name: 1 });
        return serviceZones;
      }
    } catch (error) {
      console.error('Error finding service zones:', error);
      return [];
    }
  }

  private buildUrl(h3_indexes: String[]) {
    let base_URL = 'https://h3geo.org/#hex='
    for (let i = 0; i < h3_indexes.length; i++) {
      base_URL += h3_indexes[i] + '%2C+'
    }
    return base_URL
  }

  async getSubscribedServiceZonesForUser(orgId: string, user: any) {
    try {
      let orgServiceZones = null;
      let userServiceZones = null;
      if (orgId != null) orgServiceZones = await this.organizationService.findByAuth0Id(orgId);
      if (user != null) userServiceZones = await this.userModel.findOne({ sub: user.sub });
      return {
        orgServiceZones: orgServiceZones ? orgServiceZones['service_zones'] : [],
        userServiceZones: userServiceZones ? userServiceZones['service_zones'] : [],
      };
    } catch (error) {
      console.error('Error finding service zones:', error);

      throw new BadRequestException('Failed to create service zone');
    }
  }

  async deleteServiceZoneById(id: string) {
    try {
      // Get the service zone before deleting to invalidate cache
      const serviceZoneToDelete = await this.getServiceZoneById(id);

      const result = await this.serviceZonesModel.deleteOne({ _id: id });

      // Invalidate cache for this service zone
      if(process.env.ENABLE_CACHE === 'true')
        await this.cacheManager.del(`servicezone:id:${id}`);

      // Invalidate H3 cache if service zone was found and deleted
      if (result.deletedCount > 0 && serviceZoneToDelete?.h3_indexes) {
        await this.invalidateH3Cache(serviceZoneToDelete.h3_indexes);
      }

      return result;
    } catch (error) {
      console.error('Error deleting service zone:', error);
      throw new BadRequestException('Failed to delete service zone');
    }
  }

  async updateServiceZoneById(id: string, serviceZone: any) {
    const existingServiceZone = await this.getServiceZoneById(id);
    if (!existingServiceZone) {
      throw new BadRequestException('Service zone does not exists');
    }
    try {
      const updatedServiceZone = await this.serviceZonesModel.findByIdAndUpdate(id, serviceZone);

      // Invalidate cache for this service zone
      if(process.env.ENABLE_CACHE === 'true')
        await this.cacheManager.del(CacheKeyPatterns.SERVICE_ZONE_BY_ID(id));

      // Invalidate H3 cache for old and new H3 indexes
      if (existingServiceZone.h3_indexes) {
        await this.invalidateH3Cache(existingServiceZone.h3_indexes);
      }
      if (updatedServiceZone?.h3_indexes) {
        await this.invalidateH3Cache(updatedServiceZone.h3_indexes);
      }

      return updatedServiceZone;
    } catch (error) {
      console.error('Error updating service zone:', error);
      throw new BadRequestException('Failed to update service zone');
    }
  }

  async findServiceZonesFromH3Indexes(h3_indexes: string[]) {
    try {
      // Create a cache key from sorted H3 indexes to ensure consistency
      const cacheKey = CacheKeyPatterns.SERVICE_ZONE_BY_H3(h3_indexes);

      // Try to get from cache first
      const cachedServiceZones = process.env.ENABLE_CACHE === 'true'? await this.cacheManager.get<any[]>(cacheKey) : undefined;
      if (cachedServiceZones && process.env.ENABLE_CACHE === 'true') {
        return cachedServiceZones;
      }

      // If not in cache, query database
      const serviceZones = await this.serviceZonesModel.find({
        h3_indexes: { $in: h3_indexes },
      });

      // Store in cache with 10 minute TTL (600 seconds) - service zones change infrequently
      if (serviceZones && process.env.ENABLE_CACHE === 'true') {
        await this.cacheManager.set(cacheKey, serviceZones, 600000); // 10 minutes in milliseconds
      }

      return serviceZones;
    } catch (error) {
      console.error('Error finding service zones:', error);
      throw new BadRequestException('Failed to find service zone');
    }
  }

  // Helper method to invalidate H3-related cache entries
  private async invalidateH3Cache(h3_indexes: string[]) {
    // Generate all possible cache keys that might contain these H3 indexes
    const cacheKeysToDelete = [];

    // For each H3 index, we need to invalidate cache entries that might contain it
    for (const h3Index of h3_indexes) {
      // Single index cache key using the pattern
      cacheKeysToDelete.push(CacheKeyPatterns.SERVICE_ZONE_BY_H3([h3Index]));

      // For combinations, we'd need to be more sophisticated
      // For now, we'll use a pattern-based approach
    }

    // Also invalidate sorted combinations
    cacheKeysToDelete.push(CacheKeyPatterns.SERVICE_ZONE_BY_H3(h3_indexes));

    // Delete all cache keys
    if(process.env.ENABLE_CACHE === 'true')
      await Promise.all(cacheKeysToDelete.map(key => this.cacheManager.del(key)));
  }
}
