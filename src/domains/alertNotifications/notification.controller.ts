// notifications.controller.ts
import { Controller, Get, NotFoundException, Param, Patch, Req } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { Notification } from './notification.interface';
import prepareDroneDetectionData from './drone-data.service';
import { SpectrumEventService } from '../spectrumEvents/spectrumEvent.service';

@Controller('api/notifications')
export class NotificationsController {
  constructor(private readonly notificationService: NotificationService,
    private readonly spectrumEventsService: SpectrumEventService
  ) {}

  @Get()
  async findAllForUser(@Req() req: Request): Promise<Notification[]> {
    const org_ig = req['org_id'];
    const data = await this.notificationService.findAllActiveEventsByOrgId(org_ig);

    const spectrumFlag = process.env.SPECTRUM_FLAG;
    if (spectrumFlag === 'true') {
    const activeEventsFromSpectrum = await this.spectrumEventsService.findIncompleteSpectrumEvent(org_ig);
    const activeEventsFromSpectrumIds = activeEventsFromSpectrum.map(e => e.EVENT_ID);
      const spectrumNotifications = await this.notificationService.findByMultipleEventIdAndOrgId(activeEventsFromSpectrumIds, org_ig);
      spectrumNotifications.forEach(notification => {
        notification.event=[]
        notification.event.push(activeEventsFromSpectrum.find(e => e.EVENT_ID === notification.event_id));
      });
      data.push(...spectrumNotifications);
    }
    
    const result: any[] = [];
    for (let index = 0; index < data.length; index++) {
      result.push(prepareDroneDetectionData(data[index].event[0], data[index].alertZone, data[index].droneAuthId,data[index].type))
    }
    return result;
  }

  @Patch(':id')
  async markAsSeen(@Param('id') id: string): Promise<Notification> {
    const notification = await this.notificationService.findOne(id);
    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }
    return notification;
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Notification> {
    const notification = await this.notificationService.findOne(id);
    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }
    return notification;
  }
}
