import { Document, Schema } from 'mongoose';

interface DeliveryPreferences {
  sms: boolean;
  email: boolean;
}

export interface UserPreferences extends Document {
  _id: string;
  user_id: Schema.Types.ObjectId;
  // Map Preferences
  showSatelliteViewByDefault: boolean;
  showAlertZoneBoundaries: boolean; // Renamed from showAlertZones
  showInactiveAlertZones: boolean; // New field
  showAllDroneTraffic: boolean;
  loadInitialPageOnRegionView: boolean;
  // Alert Preferences
  alerts: Map<string, boolean>;
  // Delivery Preferences
  delivery: Map<string, DeliveryPreferences>;
}
