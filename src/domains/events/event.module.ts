import { <PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EventsController } from './event.controller';
import { EventService } from './event.service';
import { EventProfileService } from './eventProfile.service';
import { EventHistoryService } from './eventHistory.service';
import { EventModel } from './event.model';
import EventProfileSchema from './eventProfile.schema';
import EventHistorySchema from './eventHistory.schema';
import Constants from 'src/common/constants';
import { OrganizationModule } from '../organizations/organization.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Constants.event_profile, schema: EventProfileSchema, collection: Constants.event_profile },
      { name: Constants.event_history, schema: EventHistorySchema, collection: Constants.event_history },
      { name: Constants.notification, schema: {}, collection: Constants.notification }, // For legacy compatibility
    ]),
    OrganizationModule,
  ],
  controllers: [EventsController],
  providers: [
    EventService,
    EventProfileService,
    EventHistoryService,
    EventModel, // Keep for backward compatibility
    Logger,
  ],
  exports: [
    EventService,
    EventProfileService,
    EventHistoryService,
    EventModel, // Export for other modules that might depend on it
  ],
})
export class EventModule {}
