import { Document } from 'mongoose';

export interface EventHistory extends Document {
  _id: string;
  DEVICE_ID: string;
  EVENT_ID: string;
  H3_INDEX: string;
  LAT: number;
  LON: number;
  ALTITUDE: number;
  SPEED: number;
  INFO: {
    radio_type: string;
    frame_time: string;
    radio_channel: string;
    radio_frequency: string;
    radio_rss: string;
    radio_bssid: string;
    radio_ssid: string;
    ODID_basicID_id: string;
    ODID_basicID_uaType: string;
    ODID_loc_status: string;
    ODID_loc_direction: string;
    ODID_loc_speed: number;
    ODID_loc_vspeed: number;
    ODID_loc_lat: number;
    ODID_loc_lon: number;
    ODID_loc_geoAlt: string;
    ODID_loc_height: string;
    ODID_operator_id: string;
    ODID_operator_type: string;
    ODID_system_lat: number;
    ODID_system_lon: number;
    ODID_system_geoAlt: string;
    ODID_selfID_type: string;
    ODID_selfID_id: string;
    OpenDroneID_loc_lat: number;
    OpenDroneID_loc_lon: number;
  };
  CMD: string;
  NODE_ID: string;
  MODULE_TYPE: string;
  TIME_STAMP: Date;
  TIME_DELAY: number;
}