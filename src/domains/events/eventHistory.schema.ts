import { Schema } from 'mongoose';



const EventHistorySchema = new Schema(
  {
    _id: String,
    DEVICE_ID: String,
    EVENT_ID: { type: String, index: true },
    H3_INDEX: String,
    LAT: Number,
    LON: Number,
    ALTITUDE: Number,
    SPEED: Number,
    INFO: {
      radio_type: String,
      frame_time: String,
      radio_channel: String,
      radio_frequency: String,
      radio_rss: String,
      radio_bssid: String,
      radio_ssid: String,
      ODID_basicID_id: String,
      ODID_basicID_uaType: String,
      ODID_loc_status: String,
      ODID_loc_direction: String,
      ODID_loc_speed: Number,
      ODID_loc_vspeed: Number,
      ODID_loc_lat: Number,
      ODID_loc_lon: Number,
      ODID_loc_geoAlt: String,
      ODID_loc_height: String,
      ODID_operator_id: String,
      ODID_operator_type: String,
      ODID_system_lat: Number,
      ODID_system_lon: Number,
      ODID_system_geoAlt: String,
      ODID_selfID_type: String,
      ODID_selfID_id: String,
      OpenDroneID_loc_lat: Number,
      OpenDroneID_loc_lon: Number,
    },
    CMD: String,
    NODE_ID: String,
    MODULE_TYPE: String,
    TIME_STAMP: Date,
    TIME_DELAY: Number,
  }
  );

export default EventHistorySchema;
