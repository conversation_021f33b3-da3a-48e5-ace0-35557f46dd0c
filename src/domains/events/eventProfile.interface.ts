import { Document } from 'mongoose';
import { EventHistory } from './eventHistory.interface';
import { BaseInterface } from 'src/common/base.interface';

export interface EventProfile extends Document, BaseInterface {
  _id: string;
  INCIDENT_ID: string;
  EVENT_ID: string;
  ALERT: number;
  ALERT_GPS: [number, number];
  ALERT_REFERENCE: string;
  ALERT_TIME: Date;
  COMPLETE: number;
  CURSOR_LAT: number;
  CURSOR_LON: number;
  DEPLOYMENT_ID: string;
  DEPLOYMENT_NAME: string;
  DEVICE_ID: string;
  DEVICE_NAME: string;
  DEVICE_TYPE: number;
  DURATION: number;
  END_LAT: number;
  END_LON: number;
  END_REFERENCE: string;
  END_TIME: Date;
  FIRST_DETECTION: Date;
  GPS: [number, number];
  INFO: {
    frame_time: string;
    wlan_radio_channel: string;
    wlan_radio_frequency: string;
    wlan_radio_signal_dbm: string;
    wlan_bssid: string;
    wlan_ssid: string;
    opendroneid: string;
    OpenDroneID_basicID_id_asc: string;
    OpenDroneID_basicID_uaType: string;
    OpenDroneID_loc_status: string;
    OpenDroneID_loc_direction: string;
    OpenDroneID_loc_speed: number;
    OpenDroneID_loc_vspeed: string;
    OpenDroneID_loc_lat: number;
    OpenDroneID_loc_lon: number;
    OpenDroneID_loc_geoAlt: string;
    OpenDroneID_loc_height: string;
    OpenDroneID_operator_id: string;
    OpenDroneID_operator_type: string;
    OpenDroneID_system_lat: number;
    OpenDroneID_system_lon: number;
  };
  LAST_DETECTION: Date;
  MANUFACTURER: string;
  MODE: number;
  REDUNDENCY: string;
  REFERENCE: string;
  START_LAT: number;
  START_LON: number;
  START_REFERENCE: string;
  START_TIME: Date;
  STATS: {
    DISTANCE: string;
    VELOCITY: number;
    DIRECTION: string;
    TOA: string;
    BEARING: string;
    ALTITUDE: string;
  };
  STATUS: number;
  TIME_STAMP: Date;
  TRACK_MODE: number;
  PROCESSED_ITEMS?: Array<EventHistory>;
}