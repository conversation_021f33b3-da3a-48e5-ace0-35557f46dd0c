import { Schema } from 'mongoose';
import { EventHistory } from './eventHistory.interface';
import { BaseSchemaFields } from 'src/common/base.schema';

const EventProfileSchema = new Schema({
  INCIDENT_ID: String,
  ALERT: Number,
  EVENT_ID: { type: String, index: true },
  ALERT_GPS: [Number],
  ALERT_REFERENCE: String,
  ALERT_TIME: Date,
  COMPLETE: Number,
  CURSOR_LAT: Number,
  CURSOR_LON: Number,
  DEPLOYMENT_ID: String,
  DEPLOYMENT_NAME: String,
  DEVICE_ID: String,
  DEVICE_NAME: String,
  DEVICE_TYPE: Number,
  DURATION: Number,
  END_LAT: Number,
  END_LON: Number,
  END_REFERENCE: String,
  END_TIME: Date,
  FIRST_DETECTION: Date,
  GPS: [Number],
  INFO: {
    frame_time: String,
    wlan_radio_channel: String,
    wlan_radio_frequency: String,
    wlan_radio_signal_dbm: String,
    wlan_bssid: String,
    wlan_ssid: String,
    opendroneid: String,
    OpenDroneID_basicID_id_asc: String,
    OpenDroneID_basicID_uaType: String,
    OpenDroneID_loc_status: String,
    OpenDroneID_loc_direction: String,
    OpenDroneID_loc_speed: Number,
    OpenDroneID_loc_vspeed: String,
    OpenDroneID_loc_lat: Number,
    OpenDroneID_loc_lon: Number,
    OpenDroneID_loc_geoAlt: String,
    OpenDroneID_loc_height: String,
    OpenDroneID_operator_id: String,
    OpenDroneID_operator_type: String,
    OpenDroneID_system_lat: Number,
    OpenDroneID_system_lon: Number,
  },
  LAST_DETECTION: Date,
  MANUFACTURER: String,
  MODE: Number,
  PROCESSED_ITEMS: Array<EventHistory>,
  REDUNDENCY: String,
  REFERENCE: String,
  START_LAT: Number,
  START_LON: Number,
  START_REFERENCE: String,
  START_TIME: Date,
  STATS: {
    DISTANCE: String,
    VELOCITY: Number,
    DIRECTION: String,
    TOA: String,
    BEARING: String,
    ALTITUDE: String,
  },
  STATUS: Number,
  TIME_STAMP: Date,
  TRACK_MODE: Number,
  ...BaseSchemaFields, 
});

export default EventProfileSchema;
