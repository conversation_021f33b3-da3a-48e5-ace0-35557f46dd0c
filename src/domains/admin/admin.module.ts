import { forwardRef, Module } from '@nestjs/common';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { CacheManagementService } from '../../utils/cache-management.service';
import { DroneModule } from '../drones/drone.module';

@Module({
  imports: [
    forwardRef(() => DroneModule),
  ],
  controllers: [AdminController],
  providers: [AdminService, CacheManagementService],
  exports: [AdminService, CacheManagementService],
})
export class AdminModule {}
