import {
  Controller,
  Post,
  Req
} from '@nestjs/common';
import { AdminService } from './admin.service';

@Controller('api/admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Post('cache/clear')
  async clearAllCache(@Req() req: Request):Promise<{ success: boolean; message: string; clearedKeys?: string[] }> {
    return this.adminService.clearAllCache();
  }
}
