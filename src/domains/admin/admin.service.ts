import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheManagementService } from '../../utils/cache-management.service';
import { DroneService } from '../drones/drone.service';
import { CacheKeyPatterns } from '../../utils/cache.utils';

@Injectable()
export class AdminService {
  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly cacheManagementService: CacheManagementService,
    @Inject(forwardRef(() => DroneService))
    private readonly droneService: DroneService
  ) {}

  async clearAllCache(): Promise<{ success: boolean; message: string; clearedKeys?: string[] }> {
    try {
      // Get all cache keys that match our patterns
      const environment = process.env.ENV;
      const envPrefix = environment === 'prod' ? '' : `${environment || 'dev'}:`;

      // Define specific cache keys to clear
      const specificKeysToTry = [
        'auth0_management_token',             // Auth0 management token cache
      ];

      // Define patterns for clearing
      const cachePatterns = [
        `${envPrefix}drone:auth:*`,          // All drone authorization cache keys
        `${envPrefix}user:sub:*`,            // User cache keys
        `${envPrefix}servicezone:*`,         // Service zone cache keys
        `${envPrefix}org:auth0:*`,           // Organization cache keys
        `${envPrefix}event:*`,               // Event profile and history cache keys
        `authorized_drone:*`,                // Legacy authorized drone cache keys
        `cache:GET:*`,                       // HTTP cache interceptor keys
      ];

      // Clear specific keys first
      const specificResult = await this.cacheManagementService.clearSpecificKeys(specificKeysToTry);

      // Clear pattern-based keys
      const patternResult = await this.cacheManagementService.clearCacheByPatterns(cachePatterns);

      // Combine results
      const allClearedKeys = [...specificResult.clearedKeys, ...patternResult.clearedKeys];

      return {
        success: specificResult.success && patternResult.success,
        message: `Successfully cleared ${allClearedKeys.length} cache entries`,
        clearedKeys: allClearedKeys.length > 0 ? allClearedKeys : undefined
      };

    } catch (error) {
      console.error('Failed to clear cache:', error);
      return {
        success: false,
        message: `Failed to clear cache: ${error.message}`
      };
    }
  }

  async clearDroneCache(droneId: string, orgId?: string): Promise<{ success: boolean; message: string }> {
    try {
      const cacheKeysToDelete = [
        CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID(droneId),
        CacheKeyPatterns.DRONE_AUTH_LATEST(droneId),
      ];

      // If orgId is provided, clear organization-specific caches and legacy cache keys
      if (orgId) {
        // Clear the authorized drones list for the organization
        cacheKeysToDelete.push(CacheKeyPatterns.AUTHORIZED_DRONES_BY_ORG_ID(orgId));

        try {
          const droneData = await this.droneService.findOne(droneId);
          if (droneData?.device_id) {
            const legacyCacheKey = `authorized_drone:org_id:${orgId}:device_id:${droneData.device_id}`;
            cacheKeysToDelete.push(legacyCacheKey);
          }
        } catch (error) {
          console.warn(`Could not fetch drone data for cache clearing: ${error.message}`);
        }
      }

      // Clear all cache keys in parallel
      await Promise.all(cacheKeysToDelete.map(key => this.cacheManager.del(key)));

      return {
        success: true,
        message: `Successfully cleared cache for drone ${droneId}`
      };

    } catch (error) {
      console.error(`Failed to clear drone cache for ${droneId}:`, error);
      return {
        success: false,
        message: `Failed to clear drone cache: ${error.message}`
      };
    }
  }

  async resetAllCache(): Promise<{ success: boolean; message: string }> {
    return this.cacheManagementService.resetAllCache();
  }

  async getCacheStats(): Promise<{ success: boolean; stats?: any; message: string }> {
    return this.cacheManagementService.getCacheStats();
  }

  async clearCacheByPattern(pattern: string): Promise<{ success: boolean; message: string; clearedKeys: string[] }> {
    // Validate pattern to prevent clearing unintended keys
    const allowedPatterns = [
      'drone:auth:',
      'user:sub:',
      'servicezone:',
      'org:auth0:',
      'event:',
      'authorized_drone:',
      'cache:GET:'
    ];

    const isValidPattern = allowedPatterns.some(allowed => pattern.includes(allowed));
    if (!isValidPattern) {
      return {
        success: false,
        message: `Pattern '${pattern}' is not allowed. Allowed patterns: ${allowedPatterns.join(', ')}`,
        clearedKeys: []
      };
    }

    return this.cacheManagementService.clearCacheByPatterns([pattern]);
  }

  async clearSpecificKeys(keys: string[]): Promise<{ success: boolean; message: string; clearedKeys: string[] }> {
    return this.cacheManagementService.clearSpecificKeys(keys);
  }

  async getSystemHealth(): Promise<{ success: boolean; health: any; message: string }> {
    try {
      const health = {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: process.env.NODE_ENV || 'development',
        version: process.version,
      };

      // Get cache stats as part of health check
      const cacheStats = await this.getCacheStats();
      if (cacheStats.success) {
        health['cache'] = cacheStats.stats;
      }

      return {
        success: true,
        health,
        message: 'System health retrieved successfully'
      };

    } catch (error) {
      console.error('Failed to get system health:', error);
      return {
        success: false,
        health: null,
        message: `Failed to get system health: ${error.message}`
      };
    }
  }
}