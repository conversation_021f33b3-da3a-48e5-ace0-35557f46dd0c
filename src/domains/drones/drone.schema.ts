import { Schema } from 'mongoose';
import { BaseSchemaFields } from 'src/common/base.schema';

const DroneSchema = new Schema({
  device_id: { type: String, required: true, unique: true },
  org_id: { type: String, required: true },
  display_name: { type: String, default: '' },
  tag: { type: String, default: '' },
  uas_id: { type: String, required: false },
  operator_id: { type: String, required: false },
  ...BaseSchemaFields
});

// Create indexes for common query patterns
DroneSchema.index({ org_id: 1, device_id: 1, uas_id: 1, operator_id: 1 });

export default DroneSchema;
