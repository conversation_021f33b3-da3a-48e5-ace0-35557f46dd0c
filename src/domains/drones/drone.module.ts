import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Drone<PERSON>ontroller } from './drone.controller';
import { DroneService } from './drone.service';
import DroneSchema from './drone.schema';
import { DroneAuthorizationModule } from '../droneAuthorizations/droneAuthorization.module';
import { NotificationModule } from '../alertNotifications/notification.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'drones', schema: DroneSchema }]),
    DroneAuthorizationModule,
    NotificationModule,
  ],
  controllers: [DroneController],
  providers: [DroneService],
  exports: [DroneService],
})
export class DroneModule {}
