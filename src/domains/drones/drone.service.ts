import { Injectable, NotFoundException, BadRequestException, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Drone } from './drone.interface';
import { DroneAuthorizationService } from '../droneAuthorizations/droneAuthorization.service';
import { NotificationService } from '../alertNotifications/notification.service';
import { CacheKeyPatterns } from '../../utils/cache.utils';
import Constants from 'src/common/constants';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class DroneService {
  // Cache TTL constants (in milliseconds)
  private static readonly CACHE_TTL_AUTHORIZED_DRONES = 300000; // 5 minutes - reasonable TTL for authorized drones list

  constructor(
    @InjectModel(Constants.drones) private droneModel: Model<Drone>,
    private droneAuthorizationService: DroneAuthorizationService,
    private notificationService: NotificationService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  private async updateNotificationWithAuthorization(notification: any, authorization: any): Promise<void> {
    if (notification.droneAuthId) {
      notification.auth_history.push(Types.ObjectId.createFromTime(notification.droneAuthId));
    }
    notification.droneAuthId = authorization.id;
    await this.notificationService.update(
      notification._id.toString(),
      {
        auth_history: notification.auth_history,
        droneAuthId: authorization._id.toString()
      }
    );
  }

  async findAuthorizedDrones(orgId: string): Promise<Drone[]> {
    const cacheKey = CacheKeyPatterns.AUTHORIZED_DRONES_BY_ORG_ID(orgId);

    // Try to get from cache first
    const cachedDrones = process.env.ENABLE_CACHE === 'true'? await this.cacheManager.get<Drone[]>(cacheKey) : null;
    if (cachedDrones && process.env.ENABLE_CACHE === 'true') {
      return cachedDrones;
    }

    // If not in cache, query database
    const drones = await this.droneModel
      .aggregate([
        {
          $match: {
            "org_id": orgId,
            "isDeleted": false,
          },
        },
        {
          $lookup: {
            from: Constants.droneAuthorizations,
            localField: '_id',
            foreignField: 'drone_id',
            as: 'authorizations',
          },
        },
        {
          $unwind: {
            "path": "$authorizations",
            "includeArrayIndex": "string",
            "preserveNullAndEmptyArrays": false
          }
        },
        {
          $match: {
            "authorizations.is_authorized": true,
            "authorizations.isDeleted": false,
            "authorizations.isActive": true,
            "authorizations": { $ne: [] }
          }
        },

        {
          $sort: {
            "createdAt": -1
          }
        }
      ])
      .exec();

    // Store in cache with TTL
    if (drones && process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, drones, DroneService.CACHE_TTL_AUTHORIZED_DRONES);
    }

    return drones;
  }

  async findOne(id: string): Promise<Drone> {
    const drone = await this.droneModel.findOne({ _id: new Types.ObjectId(id), isDeleted: false }).exec();

    if (!drone) {
      throw new NotFoundException(`Drone with ID ${id} not found`);
    }

    return drone;
  }

  async findByDeviceIdValidated(deviceId: string): Promise<Drone> {
    const drone = await this.findByDeviceId(deviceId);

    if (!drone) {
      throw new NotFoundException(`Drone with device_id ${deviceId} not found`);
    }

    return drone;
  }

  async findByDeviceId(deviceId: string): Promise<Drone> {
    return this.droneModel.findOne({ device_id: deviceId, isDeleted: false }).exec();
  }

  async create(droneData: Partial<Drone>, userId: string): Promise<Drone> {
    // Validate if device_id already exists
    const existingDrone = await this.droneModel.findOne({
      device_id: droneData.device_id,
      isDeleted: false
    }).exec();

    if (existingDrone) {
      throw new BadRequestException(`Drone with device_id ${droneData.device_id} already exists`);
    }

    // Create new drone
    const newDrone = new this.droneModel({
      ...droneData,
      createdBy: new Types.ObjectId(userId),
      updatedBy: new Types.ObjectId(userId),
    });

    return newDrone.save();
  }

  async createOrUpdateDroneWithAuthorization(
    droneData: Partial<Drone>,
    authorizationData: {
      is_authorized: boolean;
      org_id: string;
      current_event_id: string;
      authorize_expires_at: Date;
      issued_at?: Date;
      authorized_by?: string;
      notes?: string;
      alert_zones?: Types.ObjectId[];
    },
    userId: string
  ): Promise<{ drone: Drone, authorization: any }> {
    let drone = await this.findByDeviceId(droneData.device_id);
    const orgId = authorizationData.org_id || drone.org_id;

    if (!drone) {
      drone = await this.create(droneData, userId);
    }

    // Check if drone is already authorized
    const latestAuthorization = await this.droneAuthorizationService.getLatestAuthorizationForDrone(drone._id.toString());
    if (latestAuthorization) {
      if (latestAuthorization.is_authorized === authorizationData.is_authorized) {
        throw new BadRequestException(`Drone is already ${authorizationData.is_authorized ? 'authorized' : 'unauthorized'}`);
      }
    }
    
    // Then create the authorization
    const authorization = await this.droneAuthorizationService.create({
      drone_id: drone._id,
      is_authorized: authorizationData.is_authorized,
      authorize_expires_at: authorizationData.authorize_expires_at,
      authorized_by: authorizationData.authorized_by || '',
      notes: authorizationData.notes || '',
      alert_zones: authorizationData.alert_zones || [],
    }, userId, orgId);

    //authorize current notification
    if(authorizationData.current_event_id){
      const notifications = await this.notificationService.findByEventIdAndOrgId(authorizationData.current_event_id, orgId);
      if (notifications.length > 0) {
        for(let i=0;i < notifications.length; i++){
          const notification = notifications[i];
          await this.updateNotificationWithAuthorization(notification, authorization);
        }
      }
    } else {
     const notifications = await this.notificationService.findAllActiveEventsByOrgId(orgId);
     for (let index = 0; index < notifications.length; index++) {
        await this.updateNotificationWithAuthorization(notifications[index], authorization);
      }
    }

    // Explicitly invalidate the authorized drones cache for this organization
    // This ensures the cache is cleared even if there are any edge cases
    if (orgId) {
      const cacheKey = CacheKeyPatterns.AUTHORIZED_DRONES_BY_ORG_ID(orgId);
      if(process.env.ENABLE_CACHE === 'true')
        await this.cacheManager.del(cacheKey);
    }

    return { drone, authorization };
  }

  async update(id: string, droneData: Partial<Drone>, userId: string): Promise<Drone> {
    // Check if drone exists
    const drone = await this.droneModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!drone) {
      throw new NotFoundException(`Drone with ID ${id} not found`);
    }

    // If device_id is being updated, check if it's unique
    if (droneData.device_id && droneData.device_id !== drone.device_id) {
      const existingDrone = await this.droneModel.findOne({
        device_id: droneData.device_id,
        isDeleted: false,
        _id: { $ne: id }
      }).exec();

      if (existingDrone) {
        throw new BadRequestException(`Drone with device_id ${droneData.device_id} already exists`);
      }
    }

    // Update drone
    const updatedDrone = await this.droneModel.findByIdAndUpdate(
      id,
      {
        ...droneData,
        updatedBy: new Types.ObjectId(userId),
        updatedAt: new Date(),
      },
      { new: true }
    ).exec();

    return updatedDrone;
  }

  async delete(id: string, userId: string): Promise<{ message: string }> {
    const drone = await this.droneModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!drone) {
      throw new NotFoundException(`Drone with ID ${id} not found`);
    }

    // Soft delete
    await this.droneModel.findByIdAndUpdate(
      id,
      {
        isDeleted: true,
        isActive: false,
        deletedAt: new Date(),
        deletedBy: new Types.ObjectId(userId),
      }
    ).exec();

    return { message: 'Drone deleted successfully' };
  }

  async getDroneWithAuthorization(id: string): Promise<any> {
    const drone = await this.findOne(id);
    const authorization = await this.droneAuthorizationService.getLatestAuthorizationForDrone(id);

    return {
      ...drone.toJSON(),
      authorization: authorization ? {
        id: authorization._id,
        authorize_expires_at: authorization.authorize_expires_at,
        is_authorized: authorization.is_authorized,
        authorized_by: authorization.authorized_by,
        notes: authorization.notes
      } : null
    };
  }

  async getDroneWithAuthorizationByDeviceId(deviceId: string): Promise<any> {
    const drone = await this.findByDeviceIdValidated(deviceId);
    // Get the latest authorization for this drone
    const authorization = await this.droneAuthorizationService.getLatestAuthorizationForDrone(drone._id.toString());

    return {
      ...drone.toJSON(),
      authorization: authorization ? {
        id: authorization._id,
        authorize_expires_at: authorization.authorize_expires_at,
        is_authorized: authorization.is_authorized,
        authorized_by: authorization.authorized_by,
        notes: authorization.notes
      } : null
    };
  }

  async getFaaDocData(uasId: string): Promise<any> {
    try {
      // First, try to find the data in the local JSON file
      const localData = this.searchLocalFaaData(uasId);
      if (localData) {
        console.log(`Found UAS ID ${uasId} in local FAA data`);
        return {
          data: {
            "trackingNumber": localData['Tracking #'],
            "makeName": localData.Make,
            "modelName": localData.Model,
          }
        };
      }

      // If not found locally, call the FAA API
      console.log(`UAS ID ${uasId} not found in local data, calling FAA API`);
      const response = await axios.get(
        `https://uasdoc.faa.gov/api/v1/serialNumbers?itemsPerPage=8&pageIndex=0&orderBy%5B0%5D=updatedAt&orderBy%5B1%5D=DESC&findBy=serialNumber&serialNumber=${uasId}`
      , {
        headers: {
          client: "external"
        }
      }
      );

      if(response.data.data.items.length === 0){
        return {
          data: {
            "trackingNumber": "",
            "makeName": "",
            "modelName": "",
          }
        }
      }
      return {
        data: {
          "trackingNumber": response.data.data.items[0].trackingNumber,
          "makeName": response.data.data.items[0].makeName,
          "modelName": response.data.data.items[0].modelName,
        }
      }
    } catch (error) {
      throw new BadRequestException(`Failed to fetch FAA DOC data for UAS ID ${uasId}: ${error.message}`);
    }
  }

  private searchLocalFaaData(searchTerm: string): any | null {
    try {
      const dataFilePath = path.join(process.cwd(), 'data', 'faa-uas-data.json');

      if (!fs.existsSync(dataFilePath)) {
        console.log('Local FAA data file not found, will use API');
        return null;
      }

      const fileContent = fs.readFileSync(dataFilePath, 'utf8');
      const faaData: Array<any> = JSON.parse(fileContent);

      // Search in the lookup data (case-insensitive)
      const searchKey = searchTerm.substring(0,8);

      const foundItem = faaData.find(item => item['Serial_start'] !== null && item['Serial_start'].toString().startsWith(searchKey));

      if (foundItem) {
        return foundItem;
      }

      return null;
    } catch (error) {
      console.error('Error reading local FAA data:', error.message);
      return null;
    }
  }
}
