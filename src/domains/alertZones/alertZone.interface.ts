import { Document, Types } from 'mongoose';
import { BaseInterface } from 'src/common/base.interface';
import { AlertZoneStatusEnum } from 'src/common/enums/AlertZoneStatusEnum';

export interface AlertZone extends Document, BaseInterface {
  name: string;
  userId?: Types.ObjectId;
  orgId?: Types.ObjectId;
  serviceZones: Types.ObjectId[];
  geometry: {
    type: 'Polygon' | 'circle';
    coordinates: number[][][] ;
  };
  latestStatus: AlertZoneStatusEnum;
}