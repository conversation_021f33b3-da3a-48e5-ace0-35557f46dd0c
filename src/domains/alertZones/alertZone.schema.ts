import { Schema } from 'mongoose';
import { BaseSchemaFields } from 'src/common/base.schema';
import { AlertZoneStatusEnum } from 'src/common/enums/AlertZoneStatusEnum';

const AlertZoneSchema = new Schema({
  name: { type: String, required: true },
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: false },
  orgId: { type: Schema.Types.ObjectId, ref: 'organizations', required: false },
  serviceZones: [{ type: Schema.Types.ObjectId, ref: 'servicezones' }],
  geometry: {
    type: { type: String, enum: ['Polygon', 'circle'], required: true },
    coordinates: { type: [[[Number]]], required: true },
  },
  latestStatus: {type: Number, enum: Object.values(AlertZoneStatusEnum).filter(v => typeof v === 'number'), default: 1},
  ...BaseSchemaFields, 
});

AlertZoneSchema.index({ geometry: '2dsphere' });

export default AlertZoneSchema;
