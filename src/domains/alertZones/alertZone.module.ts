import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import AlertZoneSchema from './alertZone.schema';
import { AlertZoneService } from './alertZone.service';
import { AlertZoneController } from './alertZone.controller';
import { ServiceZoneModule } from '../serviceZone/serviceZone.module';
import { OrganizationModule } from '../organizations/organization.module';
import { H3Service } from 'src/utils/h3.service';
import { LoggingModule } from '../logging/logging.module';
import { UserPreferencesModel } from '../userPreferences/userPreferences.model';
import UserPreferencesSchema from '../userPreferences/userPreferences.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'alertZone', schema: AlertZoneSchema },
      { name: 'userPreferences', schema: UserPreferencesSchema }
    ]),
    ServiceZoneModule,
    OrganizationModule,
    LoggingModule,
  ],
  controllers: [AlertZoneController],
  providers: [AlertZoneService, H3Service, UserPreferencesModel],
  exports: [AlertZoneService],
})
export class AlertZoneModule {}
