import { <PERSON>, Post, Body, Get, Query, Req, Delete, Param, Patch } from '@nestjs/common';
import { AlertZoneService } from './alertZone.service';
import { UserPreferencesModel } from '../userPreferences/userPreferences.model';

@Controller('api/alertZones')
export class AlertZoneController {
  constructor(
    private readonly alertZoneService: AlertZoneService,
    private readonly userPreferencesModel: UserPreferencesModel,
  ) {}


  private async getUserShowInactivePreference(req: any): Promise<boolean> {
    // Get user preferences if user is authenticated
    if (req.user && req.user._id) {
      try {
        const userPrefs = await this.userPreferencesModel.findOne({ user_id: req.user._id });
        if (userPrefs && userPrefs.showInactiveAlertZones !== undefined) {
          return userPrefs.showInactiveAlertZones;
        }
      } catch (error) {
        console.warn('Failed to get user preferences, using default:', error);
      }
    }

    // Default value for backward compatibility
    return true;
  }

  @Post()
  async createAlertZone(@Req() req: Request, @Body() alertZoneDto: any) {
    const user = req['user'];
    return this.alertZoneService.createAlertZone(alertZoneDto, user);
  }

  @Post('organization')
  async createAlertZoneForOrg(@Req() req: Request, @Body() alertZoneDto: any) {
    const user = req['user'];
    return this.alertZoneService.createOrganizationAlertZone(alertZoneDto, user);
  }

  @Get()
  async findalertZonesContainingPoint(@Query('longitude') longitude: number, @Query('latitude') latitude: number) {
    return this.alertZoneService.findAlertZonesContainingPoint({
      longitude,
      latitude,
    });
  }

  @Get('userswithalertzones')
  async findUsersWithAlertZonesContainingPoint(@Query('longitude') longitude: number, @Query('latitude') latitude: number) {
    return this.alertZoneService.findUsersWithAlertZonesContainingPoint({
      longitude,
      latitude,
    });
  }

  @Patch(':id/status')
  async updateAlertZoneStatus(
    @Param('id') id: string,
    @Body('isActive') isActive: boolean,
    @Req() req: Request
  ) {
    const user = req['user'];
    return this.alertZoneService.updateAlertZoneStatus(id, isActive, user['_id']);
  }

  @Delete(':id')
  async deleteServiceZoneById(@Param('id') id: string, @Req() req: Request) {
    const user = req['user'];
    return this.alertZoneService.deleteAlertZoneById(id,user['_id']);
  }

  @Get('user/:userId')
  async findZonesForUser(
    @Param('userId') userId: string,
    @Req() req: any
  ) {
    // Only apply user preferences if requesting own data
    const shouldShowInactive = req.user && req.user._id === userId 
      ? await this.getUserShowInactivePreference(req)
      : true; // default to true for other users

    return this.alertZoneService.findZonesForUser(userId, shouldShowInactive);
  }

  @Get('organization/:orgId')
  async findZonesForOrganization(@Param('orgId') orgId: string, @Query('search') search: string, @Req() req: any) {
    const shouldShowInactive = await this.getUserShowInactivePreference(req);

    return this.alertZoneService.findZonesForOrganization(orgId, shouldShowInactive, search);
  }
}
