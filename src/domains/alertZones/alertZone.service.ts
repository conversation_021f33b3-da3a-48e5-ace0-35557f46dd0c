// src/domains/alertZones/alertZone.service.ts
import { BadRequestException, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId, Types } from 'mongoose';
import { User } from '../users/user.schema';
import { H3Service } from 'src/utils/h3.service';
import { ServiceZoneService } from '../serviceZone/serviceZone.service';
import { OrganizationService } from '../organizations/organization.service';
import { AlertZone } from './alertZone.interface';
import { AlertZoneStatusEnum } from 'src/common/enums/AlertZoneStatusEnum';
import { LoggingService } from '../logging/logging.service';
import { LogActionEnum } from 'src/common/enums/LogActionEnum';
import { LogEntityEnum } from 'src/common/enums/LogEntityEnum';
import { Cache } from 'cache-manager';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

@Injectable()
export class AlertZoneService {
  constructor(
    @InjectModel('alertZone') private alertZoneModel: Model<any>,
    private h3Service: H3Service,
    private serviceZone: ServiceZoneService,
    private organizationService: OrganizationService,
    private loggingService: LoggingService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {}

  async createAlertZone(alertZoneDto: any, user: User): Promise<AlertZone> {
    const createdAlertZone = new this.alertZoneModel(alertZoneDto);
    createdAlertZone.userId = user._id;

    const serviceZones = await this.validateAndGetServiceZone(createdAlertZone);

    createdAlertZone['serviceZones'] = serviceZones;

    return createdAlertZone.save();
  }

  async createOrganizationAlertZone(alertZoneDto: any, user: User): Promise<AlertZone> {
    const createdAlertZone = new this.alertZoneModel(alertZoneDto);

    if (!user.org_id) {
      throw new BadRequestException('User Does not have an organization');
    }

    const orgId = await this.organizationService.findByAuth0Id(user.org_id);
    if (!orgId) {
      throw new BadRequestException(`Organization with auth0_id ${user.org_id} not found`);
    }

    createdAlertZone.orgId = orgId;

    const serviceZones = await this.validateAndGetServiceZone(createdAlertZone);

    createdAlertZone['serviceZones'] = serviceZones;
    createdAlertZone.latestStatus = 1;

    const savedData = await createdAlertZone.save();

    return savedData
  }


  async validateAndGetServiceZone(createdAlertZone: AlertZone) {
    const h3Indexes = [];
    createdAlertZone.geometry.coordinates[0].map((coordinate) => {
      const [lng, lat] = coordinate;
      const h3Index = this.h3Service.getH3Index(lat, lng, 5);
      const exist = h3Indexes.find((h3) => h3 === h3Index);
      if (!exist) h3Indexes.push(h3Index);
    });

    //find all service zones for the h3 indexes
    const serviceZones = await this.serviceZone.findServiceZonesFromH3Indexes(h3Indexes);

    if (serviceZones.length === 0) {
      console.log("No Service Zones found creating one right now...")
      const name = createdAlertZone.name + ' Service Zone';
      const gps = this.getPolygonCenter(createdAlertZone.geometry);

      console.log("new service zone name and gps: ", name, gps)

      const serviceZone = await this.serviceZone.createServiceZoneWithGPS({ name, gps });
      console.log("Created service zone: ", serviceZone)
      serviceZones.push(serviceZone);
    }

    return serviceZones;
  }

   getPolygonCenter(geometry) {
    if (!geometry || geometry.type !== "Polygon") {
      throw new Error("Invalid geometry type. Expected GeoJSON Polygon.");
    }
  
    const coords = geometry.coordinates[0]; // Outer ring
    let area = 0;
    let x = 0;
    let y = 0;
  
    // Using the standard polygon centroid algorithm
    for (let i = 0, j = coords.length - 1; i < coords.length; j = i++) {
      const [x0, y0] = coords[j];
      const [x1, y1] = coords[i];
      const f = x0 * y1 - x1 * y0;
      area += f;
      x += (x0 + x1) * f;
      y += (y0 + y1) * f;
    }
  
    area *= 0.5;
    const centroidX = x / (6 * area);
    const centroidY = y / (6 * area);
  
    return {
      lng: centroidX,
      lat: centroidY
    };
  }
  

  async updateAlertZone(id: string, alertZoneDto: any, updatedBy: string): Promise<AlertZone> {
    console.log('updateAlertZone', id, alertZoneDto, updatedBy);

    // Find the existing alert zone first
    const existingAlertZone = await this.alertZoneModel.findOne({
      _id: new Types.ObjectId(id),
      isDeleted: false
    });

    if (!existingAlertZone) {
      throw new NotFoundException(`Alert zone with ID ${id} not found or already deleted.`);
    }

    // Prepare update data
    const updateData = {
      ...alertZoneDto,
      updatedAt: new Date(),
      updatedBy: new Types.ObjectId(updatedBy),
    };

    // If geometry is being updated, validate and update service zones
    if (alertZoneDto.geometry) {
      const tempAlertZone = { ...existingAlertZone.toObject(), ...alertZoneDto };
      const serviceZones = await this.validateAndGetServiceZone(tempAlertZone as AlertZone);
      updateData.serviceZones = serviceZones.map(sz => sz._id);
    }

    // Update the alert zone
    const updatedAlertZone = await this.alertZoneModel.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    if (!updatedAlertZone) {
      throw new NotFoundException(`Alert zone with ID ${id} not found.`);
    }

    return updatedAlertZone;
  }

  async updateAlertZoneStatus(id: string, isActive: boolean, updatedBy: string): Promise<any> {
    console.log('updateAlertZoneStatus', id, isActive, updatedBy);

    // Find the alert zone first to get its name and organization
    const alertZone = await this.alertZoneModel.findOne({
      _id: new Types.ObjectId(id),
      isDeleted: false
    });

    if (!alertZone) {
      throw new NotFoundException(`Alert zone with ID ${id} not found or already deleted.`);
    }

    // Update the alert zone status
    const result = await this.alertZoneModel
      .updateOne(
        { _id: new Types.ObjectId(id), isDeleted: false },
        {
          $set: {
            isActive,
            updatedAt: new Date(),
            updatedBy: new Types.ObjectId(updatedBy),
          },
        }
      )
      .exec();

    if (result.matchedCount === 0) {
      throw new NotFoundException(`Alert zone with ID ${id} not found or already deleted.`);
    }

    // Log the status change
    let orgId = null;
    if (alertZone.orgId) {
      const organization = await this.organizationService.findByAuth0Id(alertZone.orgId);
      if (organization) {
        orgId = organization.auth0_id;
      }
    }

    await this.loggingService.logAlertZoneStatusChange(
      id,
      isActive,
      updatedBy,
      alertZone.name,
      orgId
    );

    return { message: `Alert zone status updated to ${isActive}.` };
  }

  async findAlertZonesContainingPoint(point: { longitude: number; latitude: number }): Promise<AlertZone[]> {
    return this.alertZoneModel
      .find({
        geometry: {
          $geoIntersects: {
            $geometry: {
              type: 'Point',
              coordinates: [point.longitude, point.latitude],
            },
          },
        },
      })
      .exec();
  }

  async findUsersWithAlertZonesContainingPoint(point: { longitude: number; latitude: number }): Promise<AlertZone[]> {
    const alertZone = await this.alertZoneModel
      .find({
        geometry: {
          $geoIntersects: {
            $geometry: {
              type: 'Point',
              coordinates: [point.longitude, point.latitude],
            },
          },
        },
      })
      .populate('userId')
      .exec();

    return alertZone.map((geofence) => geofence.userId);
  }

  async findZonesForUser(userId: String, showInactive: boolean = true): Promise<any[]> {
    const query: any = {
      userId: userId,
    };

    if (!showInactive) {
      query.isActive = true;
    }

    return this.alertZoneModel
      .find(query)
      .exec();
  }

  async findZonesForOrganization(orgId: string, showInactive: boolean = true, search?: string): Promise<any[]> {
    const matchStage: any = {
      isDeleted: false,
    };

    if (!showInactive) {
      matchStage.isActive = true;
    }

    if (search) {
      matchStage.name = { $regex: search, $options: 'i' };
    }

    return this.alertZoneModel
      .aggregate([
        {
          $match: matchStage,
        },
        {
          $lookup: {
            from: 'organizations',
            localField: 'orgId',
            foreignField: '_id',
            as: 'organization',
          },
        },
        {
          $match:
            /**
             * query: The query in MQL.
             */
            {
              'organization.auth0_id': orgId,
            },
        },
        {
          $project: {
            name: true,
            geometry: true,
            serviceZones: true,
            latestStatus: true,
            isActive: true
          },
        },
      ])
      .exec();
  }

  async deleteAlertZoneById(id: string, deletedBy: string): Promise<any> {
    // Find the alert zone first to get its name and organization
    const alertZone = await this.alertZoneModel.findOne({
      _id: new Types.ObjectId(id),
      isDeleted: false
    });

    if (!alertZone) {
      throw new NotFoundException(`Alert zone with ID ${id} not found or already deleted.`);
    }

    const result = await this.alertZoneModel
      .updateOne(
        { _id: new Types.ObjectId(id), isDeleted: false }, // Ensure the document is not already deleted
        {
          $set: {
            isDeleted: true,
            isActive: false,
            deletedAt: new Date(),
            deletedBy: new Types.ObjectId(deletedBy),
          },
        }
      )
      .exec();

    if (result.matchedCount === 0) {
      throw new NotFoundException(`Alert zone with ID ${id} not found or already deleted.`);
    }

    // Log the deletion
    let orgId = null;
    if (alertZone.orgId) {
      const organization = await this.organizationService.findByAuth0Id(alertZone.orgId);
      if (organization) {
        orgId = organization.auth0_id;
      }
    }

    await this.loggingService.createLog(
      LogActionEnum.DELETE,
      LogEntityEnum.ALERT_ZONE,
      id,
      deletedBy,
      { name: alertZone.name },
      orgId
    );

    return { message: 'Alert zone soft deleted successfully.' };
  }
}
