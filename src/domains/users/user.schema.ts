import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class User extends Document {
  @Prop({ required: true })
  sub: string;

  @Prop()
  given_name: string;

  @Prop()
  family_name: string;

  @Prop()
  nickname: string;

  @Prop()
  name: string;

  @Prop()
  picture: string;

  @Prop()
  updated_at: string;

  @Prop()
  created_at: string;

  @Prop()
  email: string;

  @Prop()
  email_verified: boolean;

  @Prop()
  phone_number: string;

  @Prop()
  phone_verified: boolean;

  @Prop()
  service_zones: any[];

  @Prop()
  org_id: string;

  @Prop()
  org_name: string;

  @Prop()
  orgs: any[];

  @Prop()
  roles: any[];

  @Prop()
  last_activity: string;
}

export const UserSchema = SchemaFactory.createForClass(User);
