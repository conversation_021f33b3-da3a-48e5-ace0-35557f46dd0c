import { Schema } from 'mongoose';
import { BaseSchemaFields } from 'src/common/base.schema';

const SpectrumEventSchema = new Schema({
  event_id: { type: String, required: true, unique: true },
  org_id: { type: String, required: true },
  event_type: { type: String, required: true },
  event_data: { type: Schema.Types.Mixed, required: true },
  timestamp: { type: Date, default: Date.now },
  location: {
    latitude: { type: Number, required: false },
    longitude: { type: Number, required: false }
  },
  frequency: { type: Number, required: false },
  signal_strength: { type: Number, required: false },
  device_info: {
    device_id: { type: String, required: false },
    device_type: { type: String, required: false },
    manufacturer: { type: String, required: false }
  },
  metadata: { type: Schema.Types.Mixed, default: {} },
  ...BaseSchemaFields
});

SpectrumEventSchema.index({ org_id: 1, timestamp: -1 });
SpectrumEventSchema.index({ org_id: 1, event_id: 1 });

export default SpectrumEventSchema;
