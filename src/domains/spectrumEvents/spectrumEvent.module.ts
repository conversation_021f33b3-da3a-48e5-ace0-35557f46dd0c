import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SpectrumEventController } from './spectrumEvent.controller';
import { SpectrumEventService } from './spectrumEvent.service';
import { SpectrumEventModel } from './spectrumEvent.model';
import { SpectrumEventHistoryModel } from './spectrumEventHistory.model';
import SpectrumEventSchema from './spectrumEvent.schema';
import SpectrumEventHistorySchema from './spectrumEventHistory.schema';
import Constants from 'src/common/constants';
import { OrganizationModule } from '../organizations/organization.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Constants.spectrum_profile_events,
        schema: SpectrumEventSchema,
        collection: Constants.spectrum_profile_events
      },
      {
        name: Constants.spectrum_history_events,
        schema: SpectrumEventHistorySchema,
        collection: Constants.spectrum_history_events
      },
    ], 'spectrum'),
    OrganizationModule
  ],
  controllers: [SpectrumEventController],
  providers: [
    SpectrumEventService,
    SpectrumEventModel,
    SpectrumEventHistoryModel
  ],
  exports: [
    SpectrumEventService,
    SpectrumEventModel,
    SpectrumEventHistoryModel
  ],
})
export class SpectrumEventModule {}
