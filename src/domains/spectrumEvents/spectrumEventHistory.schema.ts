import { Schema } from 'mongoose';

const SpectrumEventHistoryInfoSchema = new Schema({
  radio_ssid: { type: String, default: '' },
  radio_channel: { type: String, default: '' },
  radio_frequency: { type: String, default: '' },
  ODID_system_lon: { type: Number, default: 0 },
  radio_bssid: { type: String, default: '' },
  ODID_loc_lon: { type: Number, default: 0 },
  radio_rss: { type: Number, default: 0 },
  wlan_radio_noise_dbm: { type: Number, default: 0 },
  ODID_loc_status: { type: Number, default: 0 },
  ODID_loc_height: { type: Number, default: 0 },
  ODID_system_lat: { type: Number, default: 0 },
  ODID_loc_geoAlt: { type: Number, default: 0 },
  ODID_basicID_id: { type: String, default: '' },
  ODID_basicID_uaType: { type: Number, default: 0 },
  ODID_loc_lat: { type: Number, default: 0 },
  ODID_loc_vspeed: { type: Number, default: 0 },
  ODID_loc_direction: { type: Number, default: 0 },
  ODID_loc_speed: { type: Number, default: 0 }
}, { _id: false });

const SpectrumEventHistorySchema = new Schema({
  EVENT_ID: { type: String, required: true },
  TOPIC: { type: String, required: true },
  NODE_ID: { type: String, default: '' },
  DEVICE_ID: { type: String, required: true },
  LAT: { type: Number, required: true },
  LON: { type: Number, required: true },
  H3_INDEX: { type: String, default: '' },
  DEVICE_NAME: { type: String, required: true },
  ALTITUDE: { type: Number, default: 0 },
  SPEED: { type: Number, default: 0 },
  CMD: { type: String, required: true },
  MODULE_TYPE: { type: String, required: true },
  TIME_STAMP: { type: Date, required: true },
  TIME_DELAY: { type: Number, default: 0 },
  INFO: { type: SpectrumEventHistoryInfoSchema, default: {} }
});

// Create indexes for common query patterns
SpectrumEventHistorySchema.index({ EVENT_ID: 1 });

export default SpectrumEventHistorySchema;
