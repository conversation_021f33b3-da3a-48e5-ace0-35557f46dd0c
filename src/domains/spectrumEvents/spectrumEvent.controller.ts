import { Controller, Get, Post, Put, Delete, Body, Param, Query, HttpException, HttpStatus, Req } from '@nestjs/common';
import { SpectrumEventService } from './spectrumEvent.service';
import { SpectrumEvent } from './spectrumEvent.interface';

@Controller('api/spectrum-events')
export class SpectrumEventController {
  constructor(private readonly spectrumEventService: SpectrumEventService) {}

  @Post()
  async createSpectrumEvent(@Body() spectrumEventData: Partial<SpectrumEvent>) {
    try {
      const createdEvent = await this.spectrumEventService.createSpectrumEvent(spectrumEventData);
      return createdEvent;
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to create spectrum event',
          error: error.message
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get(':id')
  async getSpectrumEventById(@Param('id') id: string) {
    try {
      const event = await this.spectrumEventService.findSpectrumEventById(id);
      if (!event) {
        throw new HttpException(
          {
            success: false,
            message: 'Spectrum event not found'
          },
          HttpStatus.NOT_FOUND
        );
      }
      return event;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve spectrum event',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('event/:eventId')
  async getSpectrumEventByEventId(@Req() req: Request, @Param('eventId') eventId: string) {
    try {
      const orgId = req['org_id'];
      const event = await this.spectrumEventService.findSpectrumEventByEventId(eventId, orgId);
      if (!event) {
        throw new HttpException(
          {
            success: false,
            message: 'Spectrum event not found'
          },
          HttpStatus.NOT_FOUND
        );
      }
      return event;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve spectrum event',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('organization/:orgId')
  async getSpectrumEventsByOrgId(
    @Param('orgId') orgId: string,
    @Query('limit') limit: string = '100',
    @Query('skip') skip: string = '0'
  ) {
    try {
      const events = await this.spectrumEventService.findSpectrumEventsByOrgId(
        orgId,
        parseInt(limit),
        parseInt(skip)
      );
      const totalCount = await this.spectrumEventService.getSpectrumEventCount(orgId);
      
      return {
        success: true,
        data: events,
        pagination: {
          total: totalCount,
          limit: parseInt(limit),
          skip: parseInt(skip),
          hasMore: parseInt(skip) + events.length < totalCount
        }
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve spectrum events',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('type/:eventType')
  async getSpectrumEventsByType(
    @Param('eventType') eventType: string,
    @Query('limit') limit: string = '100',
    @Query('skip') skip: string = '0'
  ) {
    try {
      const events = await this.spectrumEventService.findSpectrumEventsByType(
        eventType,
        parseInt(limit),
        parseInt(skip)
      );
      
      return {
        success: true,
        data: events,
        pagination: {
          limit: parseInt(limit),
          skip: parseInt(skip),
          count: events.length
        }
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve spectrum events by type',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('device/:deviceId')
  async getSpectrumEventsByDeviceId(
    @Param('deviceId') deviceId: string,
    @Query('limit') limit: string = '100',
    @Query('skip') skip: string = '0'
  ) {
    try {
      const events = await this.spectrumEventService.findSpectrumEventsByDeviceId(
        deviceId,
        parseInt(limit),
        parseInt(skip)
      );
      
      return {
        success: true,
        data: events,
        pagination: {
          limit: parseInt(limit),
          skip: parseInt(skip),
          count: events.length
        }
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve spectrum events by device ID',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  async getSpectrumEventsByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('orgId') orgId?: string,
    @Query('limit') limit: string = '100',
    @Query('skip') skip: string = '0'
  ) {
    try {
      if (!startDate || !endDate) {
        throw new HttpException(
          {
            success: false,
            message: 'startDate and endDate are required'
          },
          HttpStatus.BAD_REQUEST
        );
      }

      const events = await this.spectrumEventService.findSpectrumEventsByDateRange(
        new Date(startDate),
        new Date(endDate),
        orgId,
        parseInt(limit),
        parseInt(skip)
      );
      
      return {
        success: true,
        data: events,
        pagination: {
          limit: parseInt(limit),
          skip: parseInt(skip),
          count: events.length
        }
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve spectrum events by date range',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':id')
  async updateSpectrumEvent(@Param('id') id: string, @Body() updateData: Partial<SpectrumEvent>) {
    try {
      const updatedEvent = await this.spectrumEventService.updateSpectrumEvent(id, updateData);
      if (!updatedEvent) {
        throw new HttpException(
          {
            success: false,
            message: 'Spectrum event not found'
          },
          HttpStatus.NOT_FOUND
        );
      }
      return {
        success: true,
        data: updatedEvent,
        message: 'Spectrum event updated successfully'
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to update spectrum event',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':id')
  async deleteSpectrumEvent(@Param('id') id: string, @Body('deletedBy') deletedBy?: string) {
    try {
      const deletedEvent = await this.spectrumEventService.deleteSpectrumEvent(id, deletedBy);
      if (!deletedEvent) {
        throw new HttpException(
          {
            success: false,
            message: 'Spectrum event not found'
          },
          HttpStatus.NOT_FOUND
        );
      }
      return {
        success: true,
        message: 'Spectrum event deleted successfully'
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to delete spectrum event',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
