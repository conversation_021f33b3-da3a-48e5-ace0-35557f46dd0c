import { Document } from 'mongoose';

export interface SpectrumEventHistoryInfo {
  radio_ssid?: string;
  radio_channel?: string;
  radio_frequency?: string;
  ODID_system_lon?: number;
  radio_bssid?: string;
  ODID_loc_lon?: number;
  radio_rss?: number;
  wlan_radio_noise_dbm?: number;
  ODID_loc_status?: number;
  ODID_loc_height?: number;
  ODID_system_lat?: number;
  ODID_loc_geoAlt?: number;
  ODID_basicID_id?: string;
  ODID_basicID_uaType?: number;
  ODID_loc_lat?: number;
  ODID_loc_vspeed?: number;
  ODID_loc_direction?: number;
  ODID_loc_speed?: number;
}

export interface SpectrumEventHistory extends Document {
  EVENT_ID: string;
  TOPIC: string;
  NODE_ID?: string;
  DEVICE_ID: string;
  LAT: number;
  LON: number;
  H3_INDEX?: string;
  DEVICE_NAME: string;
  ALTITUDE?: number;
  SPEED?: number;
  CMD: string;
  MODULE_TYPE: string;
  TIME_STAMP: Date;
  TIME_DELAY?: number;
  INFO?: SpectrumEventHistoryInfo;
}
