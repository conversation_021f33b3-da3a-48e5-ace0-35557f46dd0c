import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Constants from 'src/common/constants';
import { SpectrumEventHistory } from './spectrumEventHistory.interface';

@Injectable()
export class SpectrumEventHistoryModel {
  constructor(
    @InjectModel(Constants.spectrum_history_events, 'spectrum')
    private readonly spectrumEventHistoryModel: Model<SpectrumEventHistory>,
  ) {}

  get model(): Model<SpectrumEventHistory> {
    return this.spectrumEventHistoryModel;
  }

  async create(spectrumEventHistoryData: Partial<SpectrumEventHistory>): Promise<SpectrumEventHistory> {
    const createdEvent = new this.spectrumEventHistoryModel(spectrumEventHistoryData);
    return await createdEvent.save();
  }

  async findById(id: string): Promise<SpectrumEventHistory | null> {
    return await this.spectrumEventHistoryModel.findById(id).exec();
  }

  async findOne(query: any): Promise<SpectrumEventHistory | null> {
    return await this.spectrumEventHistoryModel.findOne(query).exec();
  }

  async find(query: any, options?: any): Promise<SpectrumEventHistory[]> {
    let queryBuilder = this.spectrumEventHistoryModel.find(query);
    
    if (options?.sort) {
      queryBuilder = queryBuilder.sort(options.sort);
    }
    
    if (options?.limit) {
      queryBuilder = queryBuilder.limit(options.limit);
    }
    
    if (options?.skip) {
      queryBuilder = queryBuilder.skip(options.skip);
    }
    
    return await queryBuilder.exec();
  }

  async findByIdAndUpdate(id: string, updateData: Partial<SpectrumEventHistory>, options?: any): Promise<SpectrumEventHistory | null> {
    return await this.spectrumEventHistoryModel.findByIdAndUpdate(id, updateData, options).exec() as unknown as SpectrumEventHistory | null;
  }

  async updateOne(query: any, updateData: Partial<SpectrumEventHistory>): Promise<any> {
    return await this.spectrumEventHistoryModel.updateOne(query, updateData).exec();
  }

  async updateMany(query: any, updateData: Partial<SpectrumEventHistory>): Promise<any> {
    return await this.spectrumEventHistoryModel.updateMany(query, updateData).exec();
  }

  async deleteOne(query: any): Promise<any> {
    return await this.spectrumEventHistoryModel.deleteOne(query).exec();
  }

  async deleteMany(query: any): Promise<any> {
    return await this.spectrumEventHistoryModel.deleteMany(query).exec();
  }

  async findByIdAndDelete(id: string): Promise<SpectrumEventHistory | null> {
    return await this.spectrumEventHistoryModel.findByIdAndDelete(id).exec();
  }

  async countDocuments(query: any): Promise<number> {
    return await this.spectrumEventHistoryModel.countDocuments(query).exec();
  }

  async aggregate(pipeline: any[]): Promise<any[]> {
    return await this.spectrumEventHistoryModel.aggregate(pipeline).exec();
  }

  async distinct(field: string, query?: any): Promise<any[]> {
    return await this.spectrumEventHistoryModel.distinct(field, query).exec();
  }
}
