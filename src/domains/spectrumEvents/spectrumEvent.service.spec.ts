import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { SpectrumEventService } from './spectrumEvent.service';
import Constants from 'src/common/constants';

describe('SpectrumEventService', () => {
  let service: SpectrumEventService;
  let mockModel: any;

  beforeEach(async () => {
    mockModel = {
      create: jest.fn(),
      findById: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      findByIdAndUpdate: jest.fn(),
      countDocuments: jest.fn(),
      exec: jest.fn(),
      save: jest.fn(),
      sort: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SpectrumEventService,
        {
          provide: getModelToken(Constants.spectrum_events, 'spectrum'),
          useValue: mockModel,
        },
      ],
    }).compile();

    service = module.get<SpectrumEventService>(SpectrumEventService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSpectrumEvent', () => {
    it('should create a spectrum event', async () => {
      const spectrumEventData = {
        event_id: 'test-event-1',
        org_id: 'test-org',
        event_type: 'detection',
        event_data: { test: 'data' },
      };

      const mockCreatedEvent = {
        ...spectrumEventData,
        _id: 'mock-id',
        save: jest.fn().mockResolvedValue(spectrumEventData),
      };

      // Mock the constructor to return our mock event
      mockModel.mockImplementation(() => mockCreatedEvent);

      const result = await service.createSpectrumEvent(spectrumEventData);

      expect(mockModel).toHaveBeenCalledWith(spectrumEventData);
      expect(mockCreatedEvent.save).toHaveBeenCalled();
    });
  });

  describe('findSpectrumEventById', () => {
    it('should find a spectrum event by ID', async () => {
      const eventId = 'test-id';
      const mockEvent = { _id: eventId, event_id: 'test-event-1' };

      mockModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockEvent),
      });

      const result = await service.findSpectrumEventById(eventId);

      expect(mockModel.findById).toHaveBeenCalledWith(eventId);
      expect(result).toEqual(mockEvent);
    });
  });

  describe('findSpectrumEventsByOrgId', () => {
    it('should find spectrum events by organization ID', async () => {
      const orgId = 'test-org';
      const mockEvents = [
        { _id: '1', org_id: orgId, event_type: 'detection' },
        { _id: '2', org_id: orgId, event_type: 'interference' },
      ];

      mockModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue(mockEvents),
            }),
          }),
        }),
      });

      const result = await service.findSpectrumEventsByOrgId(orgId);

      expect(mockModel.find).toHaveBeenCalledWith({ org_id: orgId, isDeleted: false });
      expect(result).toEqual(mockEvents);
    });
  });

  describe('getSpectrumEventCount', () => {
    it('should return the count of spectrum events', async () => {
      const orgId = 'test-org';
      const mockCount = 5;

      mockModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCount),
      });

      const result = await service.getSpectrumEventCount(orgId);

      expect(mockModel.countDocuments).toHaveBeenCalledWith({ org_id: orgId, isDeleted: false });
      expect(result).toEqual(mockCount);
    });
  });
});
