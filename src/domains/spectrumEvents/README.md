# Spectrum Events Module

This module provides functionality for managing spectrum events in a separate MongoDB database connection.

## Overview

The Spectrum Events module is designed to handle spectrum-related events with their own dedicated database connection, separate from the main application database. This allows for better data isolation and scalability for spectrum-specific operations.

The module includes two main collections:
1. **Event Profile Spectrum** (`event_profile_spectrum`) - For spectrum event profiles
2. **Event History Spectrum** (`event_history_spectrum`) - For spectrum event history data

## Database Configuration

The module uses a separate MongoDB connection configured through environment variables:

- `SPECTRUM_MONGODB_CONNECTION_STRING`: MongoDB connection string for spectrum events
- `SPECTRUM_DATABASE_NAME`: Database name for spectrum events

### Environment Variables by Environment

- **Development**: `spectrum_events_db`
- **QA**: `spectrum_events_db_qa`
- **Staging**: `spectrum_events_db_staging`
- **Production**: `spectrum_events_db_prod`

## Schemas

### SpectrumEvent Schema (Event Profile)
- `event_id`: Unique identifier for the event (String, required, unique)
- `org_id`: Organization identifier (String, required)
- `event_type`: Type of spectrum event (String, required)
- `event_data`: Event-specific data (Mixed, required)
- `timestamp`: Event timestamp (Date, default: now)
- `location`: Optional location data with latitude and longitude
- `frequency`: Optional frequency information (Number)
- `signal_strength`: Optional signal strength (Number)
- `device_info`: Optional device information object
- `metadata`: Additional metadata (Mixed, default: {})
- Base schema fields (isActive, createdAt, updatedAt, etc.)

### SpectrumEventHistory Schema (Event History)
- `EVENT_ID`: Event identifier (String, required)
- `TOPIC`: Event topic (String, required)
- `NODE_ID`: Node identifier (String, optional)
- `DEVICE_ID`: Device identifier (String, required)
- `LAT`: Latitude (Number, required)
- `LON`: Longitude (Number, required)
- `H3_INDEX`: H3 geospatial index (String, optional)
- `DEVICE_NAME`: Device name (String, required)
- `ALTITUDE`: Altitude in meters (Number, default: 0)
- `SPEED`: Speed (Number, default: 0)
- `CMD`: Command type (String, required)
- `MODULE_TYPE`: Module type (String, required)
- `TIME_STAMP`: Event timestamp (Date, required)
- `TIME_DELAY`: Time delay (Number, default: 0)
- `INFO`: Additional information object with ODID and radio data

## API Endpoints

### Spectrum Event Profile Endpoints
- `POST /spectrum-events` - Create a new spectrum event
- `GET /spectrum-events/:id` - Get spectrum event by ID
- `GET /spectrum-events/event/:eventId` - Get by event_id
- `GET /spectrum-events/organization/:orgId` - Get by organization
- `PUT /spectrum-events/:id` - Update spectrum event
- `DELETE /spectrum-events/:id` - Delete spectrum event

### Spectrum Event History Endpoints
- `POST /spectrum-event-history` - Create new event history
- `GET /spectrum-event-history/:id` - Get by MongoDB ID
- `GET /spectrum-event-history/event/:eventId` - Get by EVENT_ID
- `GET /spectrum-event-history/topic/:topic` - Get by TOPIC
- `GET /spectrum-event-history/device/:deviceId` - Get by DEVICE_ID
- `GET /spectrum-event-history/device-name/:deviceName` - Get by DEVICE_NAME
- `GET /spectrum-event-history/module-type/:moduleType` - Get by MODULE_TYPE
- `GET /spectrum-event-history/cmd/:cmd` - Get by CMD
- `GET /spectrum-event-history/location/nearby` - Get by location (lat, lon, radius)
- `GET /spectrum-event-history/stats/summary` - Get statistics
- `GET /spectrum-event-history` - Get by date range
- `PUT /spectrum-event-history/:id` - Update event history
- `DELETE /spectrum-event-history/:id` - Delete event history

## Usage Examples

### Creating Spectrum Event History

```typescript
const eventHistoryData = {
  EVENT_ID: 'b0ab474e-8514-11f0-b7ec-4b9b213a27fd',
  TOPIC: 'DETECTION',
  DEVICE_ID: 'Video_2_AUTEL_EVO_test',
  LAT: 40.320966529596205,
  LON: -74.03252347920059,
  DEVICE_NAME: 'Drone_RF_2-AUTEL-EVO-test',
  ALTITUDE: 0,
  SPEED: 19,
  CMD: 'RID',
  MODULE_TYPE: 'SPECTRUM',
  TIME_STAMP: new Date(),
  INFO: {
    radio_ssid: '',
    ODID_basicID_id: '',
    ODID_loc_lat: 0,
    ODID_loc_lon: 0,
    // ... other INFO fields
  }
};

const result = await spectrumEventHistoryService.createSpectrumEventHistory(eventHistoryData);
```

### Querying Spectrum Event History

```typescript
// Get events by topic
const detectionEvents = await spectrumEventHistoryService.findSpectrumEventHistoryByTopic('DETECTION');

// Get events by device
const deviceEvents = await spectrumEventHistoryService.findSpectrumEventHistoryByDeviceId('Video_2_AUTEL_EVO_test');

// Get events by location
const nearbyEvents = await spectrumEventHistoryService.findSpectrumEventHistoryByLocation(40.7128, -74.0060, 10);

// Get statistics
const stats = await spectrumEventHistoryService.getSpectrumEventHistoryStats('DETECTION');
```

## Database Indexes

### Event History Indexes
- `{ EVENT_ID: 1 }`: For unique event ID lookups
- `{ DEVICE_ID: 1, TIME_STAMP: -1 }`: For device-based queries with time sorting
- `{ TOPIC: 1, TIME_STAMP: -1 }`: For topic queries with time sorting
- `{ MODULE_TYPE: 1, TIME_STAMP: -1 }`: For module type queries with time sorting
- `{ LAT: 1, LON: 1 }`: For location-based queries
- `{ TIME_STAMP: -1 }`: For time-based sorting
- `{ DEVICE_NAME: 1, TIME_STAMP: -1 }`: For device name queries with time sorting

## Testing

Run the spectrum events tests:

```bash
npm test -- --testPathPattern=spectrumEvent
```

## Architecture

The module follows the standard NestJS architecture pattern with separate components for both event profiles and event history:

- **Schemas**: Define MongoDB schemas and indexes
- **Interfaces**: TypeScript interfaces for type safety
- **Models**: Database model wrappers with common operations
- **Services**: Business logic and database operations
- **Controllers**: HTTP endpoints and request handling
- **Module**: NestJS module configuration with dependency injection

## Connection Management

The module uses a named MongoDB connection (`'spectrum'`) to ensure complete separation from the main application database. Both collections share the same connection but are logically separated for different use cases.

## Data Structure Differences

- **Event Profile**: General spectrum events with flexible schema
- **Event History**: Specific format for historical tracking with standardized fields matching the provided data structure
