import { Document } from 'mongoose';

export interface SpectrumEvent extends Document {
  event_id: string;
  org_id: string;
  event_type: string;
  event_data: any;
  timestamp: Date;
  location?: {
    latitude?: number;
    longitude?: number;
  };
  frequency?: number;
  signal_strength?: number;
  device_info?: {
    device_id?: string;
    device_type?: string;
    manufacturer?: string;
  };
  metadata?: any;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  updatedBy?: string;
  deletedAt?: Date;
  deletedBy?: string;
  isDeleted: boolean;
}
