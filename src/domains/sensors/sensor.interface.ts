import { Document, Types } from 'mongoose';
import { BaseInterface } from 'src/common/base.interface';

export interface Assignment {
  org_id?: string | null;
  auth0_id?: string | null;
  assigned_at?: Date | null;
  notes?: string | null;
  name?: string | null;
}

export interface Connectivity {
  state: number;
  committed_state: number;
  last_change?: Date | null;
  last_online?: Date | null;
  last_offline?: Date | null;
  last_error?: Date | null;
}

export interface Location {
  last_h3?: string | null;
  h3_level?: number | null;
  lat: number;
  lon: number;
  service_ids?: Types.ObjectId[];
  updated_at?: Date;
}

export interface Registration {
  first_registered?: Date;
}

export interface Runtime {
  ip?: string | null;
  hostname?: string | null;
  code_version?: string | null;
  token_env?: string | null;
}

export interface Telemetry {
  cpu?: number;
  mem?: number;
  temp?: number;
  storage_usage?: number;
}

export interface Sensor extends Document, BaseInterface {
  NODE_ID: string;
  HOST_NAME: string;
  TIME_STAMP: Date;
  assignment?: Assignment;
  connectivity: Connectivity;
  first_seen?: Date;
  location: Location;
  registration?: Registration;
  runtime?: Runtime;
  telemetry?: Telemetry;
}
