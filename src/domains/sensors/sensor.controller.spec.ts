import { Test, TestingModule } from '@nestjs/testing';
import { SensorController } from './sensor.controller';
import { SensorService } from './sensor.service';

describe('SensorController', () => {
  let controller: SensorController;
  let service: SensorService;

  beforeEach(async () => {
    const mockSensorService = {
      findAll: jest.fn(),
      findByOrgId: jest.fn(),
      findByNodeId: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      assignSensorToOrg: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      getSensorStats: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [SensorController],
      providers: [
        {
          provide: SensorService,
          useValue: mockSensorService,
        },
      ],
    }).compile();

    controller = module.get<SensorController>(SensorController);
    service = module.get<SensorService>(SensorService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getSensorStats', () => {
    it('should return sensor statistics', async () => {
      const mockStats = {
        totalSensors: 128,
        onlineSensors: 112,
        offlineSensors: 16,
      };

      jest.spyOn(service, 'getSensorStats').mockResolvedValue(mockStats);

      const result = await controller.getSensorStats();

      expect(result).toEqual(mockStats);
      expect(service.getSensorStats).toHaveBeenCalled();
    });
  });
});
