import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Req,
  ParseIntPipe,
  DefaultValuePipe
} from '@nestjs/common';
import { SensorService } from './sensor.service';
import { Sensor } from './sensor.interface';
import { CreateSensorDto } from './dto/create-sensor.dto';
import { UpdateSensorDto } from './dto/update-sensor.dto';
import { AssignmentDto } from './dto/assign-sensor.dto';
import { SensorStatsDto } from './dto/sensor-stats.dto';
import { BulkAssignmentDto } from './dto/bulk-assign-sensor.dto';

@Controller('api/sensors')
export class SensorController {
  constructor(private readonly sensorService: SensorService) {}

  @Get()
  async findAll(
    @Req() req: Request,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query('state') state: string,
    @Query('fgNumber') fgNumber: string,
    @Query('name') name: string,
  ): Promise<{ sensors: Sensor[], total: number, page: number, totalPages: number }> {
    //TODO replace this with some global check
    const roles = req['roles'];
    if (!roles.includes('ad-admin')) {
      return this.sensorService.findAll(page, pageSize, state, fgNumber, name, req['org_id']);
    }
    else {
      return this.sensorService.findAll(page, pageSize, state, fgNumber, name);
    }
  }

  @Get('sensor-stats')
  async getSensorStats(
    @Req() req: Request,
  ): Promise<SensorStatsDto> {
    const roles = req['roles'];
    if (!roles.includes('ad-admin')) {
      return this.sensorService.getSensorStats(req['org_id']);
    }
    else {
      return this.sensorService.getSensorStats();
    }
  }

  @Get('by-org/:orgId')
  async findByOrgId(@Param('orgId') orgId: string): Promise<Sensor[]> {
    return this.sensorService.findByOrgId(orgId);
  }

  @Get('by-node-id/:nodeId')
  async findByNodeId(@Param('nodeId') nodeId: string): Promise<Sensor> {
    return this.sensorService.findByNodeId(nodeId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Sensor> {
    return this.sensorService.findOne(id);
  }

  @Post()
  async create(
    @Req() req: Request, 
    @Body() sensorData: CreateSensorDto
  ): Promise<Sensor> {
    const user = req['user'];
    
    // If assignment is provided and org_id is not set, use the request org_id
    if (sensorData.assignment && !sensorData.assignment.org_id) {
      sensorData.assignment.org_id = req['org_id'];
    }

    return this.sensorService.create(sensorData, user._id);
  }

  @Post("bulk/unAssign")
  async bulkUnAssign(
    @Req() req: Request,
    @Body() node_ids: string[]
  ): Promise<AssignmentDto> {
    const updatedBy = req['user']._id;
    const orgId = req['org_id'];
    return this.sensorService.bulkUnAssignSensorFromOrg(node_ids, updatedBy, orgId);
  }

  @Post("bulk/assign")
  async bulkAssign(
    @Req() req: Request,
    @Body() assignmentDto: BulkAssignmentDto[]
  ): Promise<AssignmentDto> {
    const updatedBy = req['user']._id;
    const orgId = req['org_id'];
    return this.sensorService.bulkAssignSensorToOrg(assignmentDto, updatedBy, orgId);
  }


  @Post(":node_id/assign")
  async assign(
    @Req() req: Request,
    @Param('node_id') node_id: string,
    @Body() AssignmentDto: AssignmentDto
  ): Promise<AssignmentDto> {
    const updatedBy = req['user']._id;
    const orgId = req['org_id'];
    return this.sensorService.assignSensorToOrg(AssignmentDto, node_id, updatedBy, orgId);
  }

  @Post(":node_id/unAssign")
  async unAssign(
    @Req() req: Request,
    @Param('node_id') node_id: string,
  ): Promise<AssignmentDto> {
    const updatedBy = req['user']._id;
    console.log(req['user'])
    const orgId = req['org_id'];
    return this.sensorService.unAssignSensorFromOrg(node_id, updatedBy, orgId);
  }

  @Post(":node_id/rename")
  async rename(
    @Req() req: Request,
    @Param('node_id') node_id: string,
    @Body('name') name: string,
  ): Promise<AssignmentDto> {
    const updatedBy = req['user']._id;
    const orgId = req['org_id'];
    return this.sensorService.updateSensorName(name,node_id,updatedBy, orgId);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() sensorData: UpdateSensorDto,
    @Req() req: Request
  ): Promise<Sensor> {
    const user = req['user'];
    return this.sensorService.update(id, sensorData, user._id);
  }

  @Delete(':id')
  async delete(
    @Param('id') id: string, 
    @Req() req: Request
  ): Promise<{ message: string }> {
    const user = req['user'];
    return this.sensorService.delete(id, user._id);
  }
}
