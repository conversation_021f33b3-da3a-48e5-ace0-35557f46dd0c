import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SensorController } from './sensor.controller';
import { SensorService } from './sensor.service';
import SensorSchema from './sensor.schema';
import Constants from 'src/common/constants';
import { LoggingModule } from '../logging/logging.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Constants.sensorProfile, schema: SensorSchema }]),
    LoggingModule,
  ],
  controllers: [SensorController],
  providers: [SensorService, LoggingModule],
  exports: [SensorService, LoggingModule],
})
export class SensorModule {}
