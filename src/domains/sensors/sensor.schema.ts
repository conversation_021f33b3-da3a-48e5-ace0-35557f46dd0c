import { Schema, Types } from 'mongoose';
import { BaseSchemaFields } from 'src/common/base.schema';

// Assignment sub-schema
const AssignmentSchema = new Schema({
  org_id: { type: Types.ObjectId, default: null },
  auth0_id: { type: String, default: null },
  assigned_at: { type: Date, default: null },
  notes: { type: String, default: null },
  name: { type: String, default: null }
}, { _id: false });

// Connectivity sub-schema
const ConnectivitySchema = new Schema({
  state: { type: Number, required: true, default: 0 },
  committed_state: { type: Number, required: true, default: 0 },
  last_change: { type: Date, default: null },
  last_online: { type: Date, default: null },
  last_offline: { type: Date, default: null },
  last_error: { type: Date, default: null }
}, { _id: false });

// Location sub-schema
const LocationSchema = new Schema({
  last_h3: { type: String, default: null },
  h3_level: { type: Number, default: null },
  lat: { type: Number, required: true },
  lon: { type: Number, required: true },
  service_ids: [{ type: Schema.Types.ObjectId, ref: 'servicezones' }],
  updated_at: { type: Date, default: Date.now }
}, { _id: false });

// Registration sub-schema
const RegistrationSchema = new Schema({
  first_registered: { type: Date, default: Date.now }
}, { _id: false });

// Runtime sub-schema
const RuntimeSchema = new Schema({
  ip: { type: String, default: null },
  hostname: { type: String, default: null },
  code_version: { type: String, default: null },
  token_env: { type: String, default: null }
}, { _id: false });

// Telemetry sub-schema
const TelemetrySchema = new Schema({
  cpu: { type: Number, default: 0 },
  mem: { type: Number, default: 0 },
  temp: { type: Number, default: 0 },
  storage_usage: { type: Number, default: 0 }
}, { _id: false });

// Main Sensor schema
const SensorSchema = new Schema({
  NODE_ID: { type: String, required: true },
  HOST_NAME: { type: String, required: true },
  TIME_STAMP: { type: Date, required: true, default: Date.now },
  assignment: { type: AssignmentSchema, default: () => ({}) },
  connectivity: { type: ConnectivitySchema, required: true },
  first_seen: { type: Date, default: Date.now },
  location: { type: LocationSchema, required: true },
  registration: { type: RegistrationSchema, default: () => ({}) },
  runtime: { type: RuntimeSchema, default: () => ({}) },
  telemetry: { type: TelemetrySchema, default: () => ({}) },
  ...BaseSchemaFields
});

// Create indexes for common query patterns
SensorSchema.index({ NODE_ID: 1 }, { unique: true });
SensorSchema.index({ HOST_NAME: 1 });
SensorSchema.index({ 'assignment.org_id': 1 });
SensorSchema.index({ 'assignment.auth0_id': 1 });
SensorSchema.index({ 'connectivity.state': 1 });
SensorSchema.index({ 'connectivity.last_online': -1 });
SensorSchema.index({ 'location.lat': 1, 'location.lon': 1 });
SensorSchema.index({ 'location.last_h3': 1 });
SensorSchema.index({ 'location.service_ids': 1 });
SensorSchema.index({ TIME_STAMP: -1 });
SensorSchema.index({ first_seen: -1 });
SensorSchema.index({ isDeleted: 1, isActive: 1 });

export default SensorSchema;
