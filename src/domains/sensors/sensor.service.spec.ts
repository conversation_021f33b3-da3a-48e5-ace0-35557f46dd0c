import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { SensorService } from './sensor.service';
import Constants from 'src/common/constants';

describe('SensorService', () => {
  let service: SensorService;
  let mockSensorModel: any;
  let mockCacheManager: any;

  beforeEach(async () => {
    mockSensorModel = {
      aggregate: jest.fn().mockReturnValue({
        exec: jest.fn(),
      }),
      findOne: jest.fn(),
      find: jest.fn(),
      countDocuments: jest.fn(),
      findByIdAndUpdate: jest.fn(),
      save: jest.fn(),
    };

    mockCacheManager = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SensorService,
        {
          provide: getModelToken(Constants.sensorProfile),
          useValue: mockSensorModel,
        },
        {
          provide: CACHE_MANAGER,
          useValue: mockCacheManager,
        },
      ],
    }).compile();

    service = module.get<SensorService>(SensorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getSensorStats', () => {
    it('should return sensor statistics', async () => {
      const mockStats = [
        {
          totalSensors: 128,
          onlineSensors: 112,
          offlineSensors: 16,
        },
      ];

      mockSensorModel.aggregate().exec.mockResolvedValue(mockStats);
      mockCacheManager.get.mockResolvedValue(null);

      const result = await service.getSensorStats();

      expect(result).toEqual({
        totalSensors: 128,
        onlineSensors: 112,
        offlineSensors: 16,
      });

      expect(mockSensorModel.aggregate).toHaveBeenCalledWith([
        {
          $match: {
            isDeleted: { $ne: true }
          }
        },
        {
          $group: {
            _id: null,
            totalSensors: { $sum: 1 },
            onlineSensors: {
              $sum: {
                $cond: [{ $eq: ['$connectivity.state', 1] }, 1, 0]
              }
            },
            offlineSensors: {
              $sum: {
                $cond: [{ $ne: ['$connectivity.state', 1] }, 1, 0]
              }
            }
          }
        },
        {
          $project: {
            _id: 0,
            totalSensors: 1,
            onlineSensors: 1,
            offlineSensors: 1
          }
        }
      ]);
    });

    it('should return default values when no sensors exist', async () => {
      mockSensorModel.aggregate().exec.mockResolvedValue([]);
      mockCacheManager.get.mockResolvedValue(null);

      const result = await service.getSensorStats();

      expect(result).toEqual({
        totalSensors: 0,
        onlineSensors: 0,
        offlineSensors: 0,
      });
    });

    it('should return cached result when available', async () => {
      const cachedStats = {
        totalSensors: 100,
        onlineSensors: 80,
        offlineSensors: 20,
      };

      process.env.ENABLE_CACHE = 'true';
      mockCacheManager.get.mockResolvedValue(cachedStats);

      const result = await service.getSensorStats();

      expect(result).toEqual(cachedStats);
      expect(mockSensorModel.aggregate).not.toHaveBeenCalled();
    });
  });
});
