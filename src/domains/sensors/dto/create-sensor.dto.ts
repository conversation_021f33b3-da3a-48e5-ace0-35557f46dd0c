import { IsString, <PERSON><PERSON><PERSON>ber, IsOptional, IsDate, IsArray, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { Types } from 'mongoose';

export class AssignmentDto {
  @IsOptional()
  @IsString()
  org_id?: string | null;

  @IsOptional()
  @IsString()
  auth0_id?: string | null;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  assigned_at?: Date | null;

  @IsOptional()
  @IsString()
  notes?: string | null;
}

export class ConnectivityDto {
  @IsNumber()
  state: number;

  @IsNumber()
  committed_state: number;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  last_change?: Date | null;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  last_online?: Date | null;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  last_offline?: Date | null;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  last_error?: Date | null;
}

export class LocationDto {
  @IsOptional()
  @IsString()
  last_h3?: string | null;

  @IsOptional()
  @IsNumber()
  h3_level?: number | null;

  @IsNumber()
  lat: number;

  @IsNumber()
  lon: number;

  @IsOptional()
  @IsArray()
  service_ids?: Types.ObjectId[];

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  updated_at?: Date;
}

export class RegistrationDto {
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  first_registered?: Date;
}

export class RuntimeDto {
  @IsOptional()
  @IsString()
  ip?: string | null;

  @IsOptional()
  @IsString()
  hostname?: string | null;

  @IsOptional()
  @IsString()
  code_version?: string | null;

  @IsOptional()
  @IsString()
  token_env?: string | null;
}

export class TelemetryDto {
  @IsOptional()
  @IsNumber()
  cpu?: number;

  @IsOptional()
  @IsNumber()
  mem?: number;

  @IsOptional()
  @IsNumber()
  temp?: number;

  @IsOptional()
  @IsNumber()
  storage_usage?: number;
}

export class CreateSensorDto {
  @IsString()
  NODE_ID: string;

  @IsString()
  HOST_NAME: string;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  TIME_STAMP?: Date;

  @IsOptional()
  @ValidateNested()
  @Type(() => AssignmentDto)
  assignment?: AssignmentDto;

  @ValidateNested()
  @Type(() => ConnectivityDto)
  connectivity: ConnectivityDto;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  first_seen?: Date;

  @ValidateNested()
  @Type(() => LocationDto)
  location: LocationDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => RegistrationDto)
  registration?: RegistrationDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => RuntimeDto)
  runtime?: RuntimeDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => TelemetryDto)
  telemetry?: TelemetryDto;
}
