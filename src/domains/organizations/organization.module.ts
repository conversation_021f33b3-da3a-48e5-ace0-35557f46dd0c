import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { OrganizationService } from './organization.service';
import { OrganizationsModel } from './organization.model';
import { OrganizationController } from './organization.controller';
import OrganizationSchema from './organization.schema';
import { UtilsModule } from 'src/utils/UtilsModule';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'organizations', schema: OrganizationSchema }]),
    UtilsModule,
  ],
  controllers: [OrganizationController],
  providers: [OrganizationService, OrganizationsModel],
  exports: [OrganizationService, OrganizationsModel],
})
export class OrganizationModule {}
