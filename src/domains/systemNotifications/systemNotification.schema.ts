import { ObjectId, Schema , Types} from 'mongoose';
import { BaseSchemaFields } from 'src/common/base.schema';
import { NotificationTypeEnum } from 'src/common/enums/NotifocationTypeEnum';

const SystemNotificationSchema = new Schema({
  org_id: Types.ObjectId,
  title: String,
  description: String,
  type: {
    type: String,
    enum: Object.values(NotificationTypeEnum),
  }, 
  seen_by: Array<ObjectId>, 
  icon: String,
  meta_data: Object,
  ...BaseSchemaFields,
  createdBy: { type: String, required: false },

  });

SystemNotificationSchema.index({ org_id: 1 });
SystemNotificationSchema.index({ createdAt: -1, org_id: 1, seen_by: 1 });

export default SystemNotificationSchema;
