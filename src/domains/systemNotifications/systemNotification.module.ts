import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import SystemNotificationSchema from './systemNotification.schema';
import { SystemNotificationService } from './systemNotification.service';
import { OrganizationModule } from '../organizations/organization.module';
import { AppSyncService } from 'src/utils/appsync.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'system_notification', schema: SystemNotificationSchema }]),
    OrganizationModule,
  ],
  providers: [SystemNotificationService, AppSyncService],
  exports: [SystemNotificationService, AppSyncService],
})
export class SystemNotificationModule {}
