import { Document, Types } from 'mongoose';
import { BaseInterface } from 'src/common/base.interface';
import { NotificationTypeEnum } from 'src/common/enums/NotifocationTypeEnum';

export interface SystemNotification extends Document, BaseInterface {
  _id: Types.ObjectId,
  org_id: Types.ObjectId,
  title: String,
  description: String,
  type: NotificationTypeEnum, 
  seen_by: Array<Types.ObjectId>, 
  icon: String,
  meta_data: any
}
