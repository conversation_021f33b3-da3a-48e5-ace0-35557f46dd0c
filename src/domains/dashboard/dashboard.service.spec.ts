import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { DashboardService } from './dashboard.service';
import { OrganizationService } from '../organizations/organization.service';
import Constants from '../../common/constants';

describe('DashboardService', () => {
  let service: DashboardService;
  let mockDroneModel: any;
  let mockAlertZoneModel: any;
  let mockDroneAuthorizationModel: any;
  let mockLogModel: any;
  let mockNotificationModel: any;
  let mockOrganizationService: any;

  beforeEach(async () => {
    // Mock models
    mockDroneModel = {
      aggregate: jest.fn(),
    };
    mockAlertZoneModel = {
      aggregate: jest.fn(),
    };
    mockDroneAuthorizationModel = {
      aggregate: jest.fn(),
    };
    mockLogModel = {
      aggregate: jest.fn(),
    };
    mockNotificationModel = {
      aggregate: jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue([]),
      }),
    };

    // Mock organization service
    mockOrganizationService = {
      findByAuth0Id: jest.fn().mockResolvedValue({ _id: 'mock-org-id' }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DashboardService,
        {
          provide: getModelToken(Constants.drones),
          useValue: mockDroneModel,
        },
        {
          provide: getModelToken(Constants.alertZones),
          useValue: mockAlertZoneModel,
        },
        {
          provide: getModelToken(Constants.droneAuthorizations),
          useValue: mockDroneAuthorizationModel,
        },
        {
          provide: getModelToken('logs'),
          useValue: mockLogModel,
        },
        {
          provide: getModelToken(Constants.notification),
          useValue: mockNotificationModel,
        },
        {
          provide: OrganizationService,
          useValue: mockOrganizationService,
        },
      ],
    }).compile();

    service = module.get<DashboardService>(DashboardService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getDashboardStats', () => {
    it('should return dashboard stats with daily grouping by default', async () => {
      // Mock the aggregation results
      const mockResults = [
        [{ matchedCount: 10 }], // result1
        [{ matchedCount: 5 }],  // result2
        [{ avgDuration: 120 }], // result3
        [],                     // result5 (groupedEvents)
        [],                     // result6 (dronesPerUasId)
        [],                     // result7 (eventsPerAlertZones)
        [],                     // result8 (eventsPerHour)
      ];

      mockNotificationModel.aggregate.mockReturnValue({
        exec: jest.fn()
          .mockResolvedValueOnce(mockResults[0])
          .mockResolvedValueOnce(mockResults[1])
          .mockResolvedValueOnce(mockResults[2])
          .mockResolvedValueOnce(mockResults[3])
          .mockResolvedValueOnce(mockResults[4])
          .mockResolvedValueOnce(mockResults[5])
          .mockResolvedValueOnce(mockResults[6]),
      });

      const result = await service.getDashboardStats(
        'test-org-id',
        ['alert-zone-1'],
        '2024-01-01',
        '2024-01-31',
        ['uas-1'],
        'daily'
      );

      expect(result).toEqual({
        totalEvents: 10,
        uniqueDrones: 5,
        averageDuration: 120,
        averageEventsPerDay: expect.any(Number),
        groupedEvents: [],
        groupBy: 'daily',
        dronesPerUasId: [],
        eventsPerAlertZones: [],
        eventsPerHour: [],
      });
    });

    it('should return dashboard stats with weekly grouping', async () => {
      // Mock the aggregation results
      const mockResults = [
        [{ matchedCount: 70 }], // result1
        [{ matchedCount: 5 }],  // result2
        [{ avgDuration: 120 }], // result3
        [],                     // result5 (groupedEvents)
        [],                     // result6 (dronesPerUasId)
        [],                     // result7 (eventsPerAlertZones)
        [],                     // result8 (eventsPerHour)
      ];

      mockNotificationModel.aggregate.mockReturnValue({
        exec: jest.fn()
          .mockResolvedValueOnce(mockResults[0])
          .mockResolvedValueOnce(mockResults[1])
          .mockResolvedValueOnce(mockResults[2])
          .mockResolvedValueOnce(mockResults[3])
          .mockResolvedValueOnce(mockResults[4])
          .mockResolvedValueOnce(mockResults[5])
          .mockResolvedValueOnce(mockResults[6]),
      });

      const result = await service.getDashboardStats(
        'test-org-id',
        ['alert-zone-1'],
        '2024-01-01',
        '2024-01-31',
        ['uas-1'],
        'weekly'
      );

      expect(result).toEqual({
        totalEvents: 70,
        uniqueDrones: 5,
        averageDuration: 120,
        averageEventsPerWeek: expect.any(Number),
        groupedEvents: [],
        groupBy: 'weekly',
        dronesPerUasId: [],
        eventsPerAlertZones: [],
        eventsPerHour: [],
      });
    });

    it('should return dashboard stats with monthly grouping', async () => {
      // Mock the aggregation results
      const mockResults = [
        [{ matchedCount: 300 }], // result1
        [{ matchedCount: 5 }],   // result2
        [{ avgDuration: 120 }],  // result3
        [],                      // result5 (groupedEvents)
        [],                      // result6 (dronesPerUasId)
        [],                      // result7 (eventsPerAlertZones)
        [],                      // result8 (eventsPerHour)
      ];

      mockNotificationModel.aggregate.mockReturnValue({
        exec: jest.fn()
          .mockResolvedValueOnce(mockResults[0])
          .mockResolvedValueOnce(mockResults[1])
          .mockResolvedValueOnce(mockResults[2])
          .mockResolvedValueOnce(mockResults[3])
          .mockResolvedValueOnce(mockResults[4])
          .mockResolvedValueOnce(mockResults[5])
          .mockResolvedValueOnce(mockResults[6]),
      });

      const result = await service.getDashboardStats(
        'test-org-id',
        ['alert-zone-1'],
        '2024-01-01',
        '2024-12-31',
        ['uas-1'],
        'monthly'
      );

      expect(result).toEqual({
        totalEvents: 300,
        uniqueDrones: 5,
        averageDuration: 120,
        averageEventsPerMonth: expect.any(Number),
        groupedEvents: [],
        groupBy: 'monthly',
        dronesPerUasId: [],
        eventsPerAlertZones: [],
        eventsPerHour: [],
      });
    });

    it('should return dashboard stats with yearly grouping', async () => {
      // Mock the aggregation results
      const mockResults = [
        [{ matchedCount: 3650 }], // result1
        [{ matchedCount: 5 }],    // result2
        [{ avgDuration: 120 }],   // result3
        [],                       // result5 (groupedEvents)
        [],                       // result6 (dronesPerUasId)
        [],                       // result7 (eventsPerAlertZones)
        [],                       // result8 (eventsPerHour)
      ];

      mockNotificationModel.aggregate.mockReturnValue({
        exec: jest.fn()
          .mockResolvedValueOnce(mockResults[0])
          .mockResolvedValueOnce(mockResults[1])
          .mockResolvedValueOnce(mockResults[2])
          .mockResolvedValueOnce(mockResults[3])
          .mockResolvedValueOnce(mockResults[4])
          .mockResolvedValueOnce(mockResults[5])
          .mockResolvedValueOnce(mockResults[6]),
      });

      const result = await service.getDashboardStats(
        'test-org-id',
        ['alert-zone-1'],
        '2024-01-01',
        '2024-12-31',
        ['uas-1'],
        'yearly'
      );

      expect(result).toEqual({
        totalEvents: 3650,
        uniqueDrones: 5,
        averageDuration: 120,
        averageEventsPerYear: expect.any(Number),
        groupedEvents: [],
        groupBy: 'yearly',
        dronesPerUasId: [],
        eventsPerAlertZones: [],
        eventsPerHour: [],
      });
    });
  });
});
