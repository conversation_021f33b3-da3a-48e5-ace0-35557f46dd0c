import { Controller, Get, Query, Req, BadRequestException } from '@nestjs/common';
import { DashboardService } from './dashboard.service';

@Controller('api/dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('/stats')
  async getDashboardStats(
    @Req() req: Request,
    @Query('alertZoneIds') alertZoneIds?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('uasIds') uasIds?: string,
    @Query('groupBy') groupBy?: 'daily' | 'weekly' | 'monthly' | 'yearly',
  ) {
    const orgId = req['org_id'];

    let alertZoneIdsArray: string[] | undefined;
    let uasIdsArray: string[] | undefined;
    if (alertZoneIds) alertZoneIdsArray = alertZoneIds.split(',').map(id => id.trim());
    if (uasIds) uasIdsArray = uasIds.split(',').map(id => id.trim());

    // Validate groupBy parameter
    const validGroupByValues = ['daily', 'weekly', 'monthly', 'yearly'];
    if (groupBy && !validGroupByValues.includes(groupBy)) {
      throw new BadRequestException(`Invalid groupBy value. Must be one of: ${validGroupByValues.join(', ')}`);
    }

    // Default to daily if not specified
    const timeGrouping = groupBy || 'daily';

    return this.dashboardService.getDashboardStats(orgId, alertZoneIdsArray, startDate, endDate, uasIdsArray, timeGrouping);
  }
}
