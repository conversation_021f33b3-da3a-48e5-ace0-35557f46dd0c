import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';

describe('DashboardController', () => {
  let controller: DashboardController;
  let mockDashboardService: any;

  beforeEach(async () => {
    mockDashboardService = {
      getDashboardStats: jest.fn().mockResolvedValue({
        totalEvents: 100,
        uniqueDrones: 10,
        averageDuration: 120,
        averageEventsPerDay: 5,
        groupedEvents: [],
        groupBy: 'daily',
        dronesPerUasId: [],
        eventsPerAlertZones: [],
        eventsPerHour: [],
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardController],
      providers: [
        {
          provide: DashboardService,
          useValue: mockDashboardService,
        },
      ],
    }).compile();

    controller = module.get<DashboardController>(DashboardController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getDashboardStats', () => {
    const mockRequest = {
      org_id: 'test-org-id',
    } as any;

    it('should return dashboard stats with default daily grouping', async () => {
      const result = await controller.getDashboardStats(
        mockRequest,
        'alert-zone-1',
        '2024-01-01',
        '2024-01-31',
        'uas-1'
      );

      expect(mockDashboardService.getDashboardStats).toHaveBeenCalledWith(
        'test-org-id',
        ['alert-zone-1'],
        '2024-01-01',
        '2024-01-31',
        ['uas-1'],
        'daily'
      );
      expect(result.groupBy).toBe('daily');
    });

    it('should return dashboard stats with weekly grouping', async () => {
      const result = await controller.getDashboardStats(
        mockRequest,
        'alert-zone-1',
        '2024-01-01',
        '2024-01-31',
        'uas-1',
        'weekly'
      );

      expect(mockDashboardService.getDashboardStats).toHaveBeenCalledWith(
        'test-org-id',
        ['alert-zone-1'],
        '2024-01-01',
        '2024-01-31',
        ['uas-1'],
        'weekly'
      );
    });

    it('should return dashboard stats with monthly grouping', async () => {
      const result = await controller.getDashboardStats(
        mockRequest,
        'alert-zone-1',
        '2024-01-01',
        '2024-01-31',
        'uas-1',
        'monthly'
      );

      expect(mockDashboardService.getDashboardStats).toHaveBeenCalledWith(
        'test-org-id',
        ['alert-zone-1'],
        '2024-01-01',
        '2024-01-31',
        ['uas-1'],
        'monthly'
      );
    });

    it('should return dashboard stats with yearly grouping', async () => {
      const result = await controller.getDashboardStats(
        mockRequest,
        'alert-zone-1',
        '2024-01-01',
        '2024-01-31',
        'uas-1',
        'yearly'
      );

      expect(mockDashboardService.getDashboardStats).toHaveBeenCalledWith(
        'test-org-id',
        ['alert-zone-1'],
        '2024-01-01',
        '2024-01-31',
        ['uas-1'],
        'yearly'
      );
    });

    it('should throw BadRequestException for invalid groupBy value', async () => {
      await expect(
        controller.getDashboardStats(
          mockRequest,
          'alert-zone-1',
          '2024-01-01',
          '2024-01-31',
          'uas-1',
          'invalid' as any
        )
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle comma-separated alertZoneIds and uasIds', async () => {
      await controller.getDashboardStats(
        mockRequest,
        'alert-zone-1,alert-zone-2,alert-zone-3',
        '2024-01-01',
        '2024-01-31',
        'uas-1,uas-2,uas-3',
        'daily'
      );

      expect(mockDashboardService.getDashboardStats).toHaveBeenCalledWith(
        'test-org-id',
        ['alert-zone-1', 'alert-zone-2', 'alert-zone-3'],
        '2024-01-01',
        '2024-01-31',
        ['uas-1', 'uas-2', 'uas-3'],
        'daily'
      );
    });

    it('should handle undefined alertZoneIds and uasIds', async () => {
      await controller.getDashboardStats(
        mockRequest,
        undefined,
        '2024-01-01',
        '2024-01-31',
        undefined,
        'daily'
      );

      expect(mockDashboardService.getDashboardStats).toHaveBeenCalledWith(
        'test-org-id',
        undefined,
        '2024-01-01',
        '2024-01-31',
        undefined,
        'daily'
      );
    });
  });
});
