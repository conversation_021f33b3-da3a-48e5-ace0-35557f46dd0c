import { Body, Controller, Get, Query } from '@nestjs/common';
import { PythonExecutionService } from './pythonExecution.service';

@Controller('python')
export class PythonExecutionController {
  constructor(
    private readonly pythonExecutionService: PythonExecutionService,
  ) {}

  @Get('liveSimulation')
  executeLiveSimulationScript(
    @Query('longitude') lng: number,
    @Query('latitude') lat: number,
    @Query('range') range: number,
  ) {
    this.pythonExecutionService.executeLiveSimulationScript(lat, lng, range);
    return { message: 'Python script execution started' };
  }

  @Get('heartBeatSimulation')
  executeHeartbeatScript(
    @Query('latitude') lat: string,
    @Query('longitude') lng: string,
    @Query('length') length: string,
    @Query('time_gap') time_gap: string,
    @Query('status') status: string,
  ) {
    this.pythonExecutionService.executeHeartbeatSimulationScript(lat, lng, length, time_gap, status);
    return { message: 'Python script execution started' };
  }
}
