import { Injectable, Logger } from '@nestjs/common';
import { spawn } from 'child_process';
import * as path from 'path';

@Injectable()
export class PythonExecutionService {
  private readonly logger = new Logger(PythonExecutionService.name);

  private handlePythonStdout(data: Buffer): void {
    const text: string[] = data.toString().split('\n').filter(Boolean);
    text.forEach(line => {
      if (line.includes('[INFO]')) {
        this.logger.log(`[py] ${line}`);
      } else if (line.includes('[WARNING]') || line.includes('WARNING') || line.includes('DeprecationWarning')) {
        this.logger.warn(`[py] ${line}`);
      } else if (line.includes('[ERROR]') || line.includes('ERROR') || line.includes('CRITICAL')) {
        this.logger.error(`[py] ${line}`);
      } else if (line.includes('[DEBUG]')) {
        this.logger.debug(`[py] ${line}`);
      } else {
        // For any other output (like print statements), log as info
        this.logger.log(`[py] ${line}`);
      }
    });
  }

  private handlePythonStderr(data: Buffer): void {
    const text: string[] = data.toString().split('\n').filter(Boolean);
    text.forEach(line => {
      // Parse the actual Python log format: timestamp [LEVEL] message
      if (line.includes('[INFO]')) {
        this.logger.log(`[py] ${line}`);
      } else if (line.includes('[WARNING]') || line.includes('WARNING') || line.includes('DeprecationWarning')) {
        this.logger.warn(`[py] ${line}`);
      } else if (line.includes('[ERROR]') || line.includes('ERROR') || line.includes('CRITICAL')) {
        this.logger.error(`[py] ${line}`);
      } else if (line.includes('[DEBUG]')) {
        this.logger.debug(`[py] ${line}`);
      } else if (line.trim()) {
        // For other stderr output that doesn't match Python logging format
        this.logger.error(`[py stderr] ${line}`);
      }
    });
  }

  private handlePythonProcessClose(code: number | null): void {
    if (code === 0) {
      this.logger.log(`Python script completed successfully with code ${code}`);
    } else {
      this.logger.error(`Python script exited with error code ${code}`);
    }
  }

  executeLiveSimulationScript(lat: number, lng: number, range: number) {
    const scriptPath = path.join(__dirname, '..', '..', '..', 'pyScripts', "ridSimulation.py");
    this.logger.log(`Executing Python script at: ${scriptPath}`);
    this.logger.log(`Current working directory: ${process.cwd()}`);

    // After installing libraries, run the Python script
    const pythonProcess = spawn('python3', [scriptPath, '-A', lat.toString(), '-O', lng.toString(), '-l', range.toString()], {
      env: { ...process.env, PYTHONUNBUFFERED: '1' }
    });

    pythonProcess.stdout.on('data', (data) => this.handlePythonStdout(data));
    pythonProcess.stderr.on('data', (data) => this.handlePythonStderr(data));
    pythonProcess.on('close', (code) => this.handlePythonProcessClose(code));
  }

  executeHeartbeatSimulationScript(lat: string, lng: string, length: string, time_gap: string, status: string) {
    const scriptPath = path.join(__dirname, '..', '..', '..', 'pyScripts', "heartbeatSim.py");
    this.logger.log(`Executing Python script at: ${scriptPath}`);
    this.logger.log(`Current working directory: ${process.cwd()}`);

    const pythonProcess = spawn('python3', [scriptPath, '-l', lat, '-o', lng, '-n', length, '-g', time_gap, '-s', status], {
      env: { ...process.env, PYTHONUNBUFFERED: '1' }
    });

    pythonProcess.stdout.on('data', (data) => this.handlePythonStdout(data));
    pythonProcess.stderr.on('data', (data) => this.handlePythonStderr(data));
    pythonProcess.on('close', (code) => this.handlePythonProcessClose(code));
  }
}
