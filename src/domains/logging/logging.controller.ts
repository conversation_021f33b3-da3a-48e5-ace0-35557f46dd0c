import { Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import { LoggingService } from './logging.service';
import { LogEntityEnum } from 'src/common/enums/LogEntityEnum';

@Controller('api/logs')
export class LoggingController {
  constructor(private readonly loggingService: LoggingService) {}

  @Get('entity/:entity/:entityId')
  async getLogsForEntity(
    @Param('entity') entity: string,
    @Param('entityId') entityId: string,
    @Query('page') page = '1',
    @Query('limit') limit = '10',
    @Req() req: Request,
  ) {
    // Validate entity type
    if (!Object.values(LogEntityEnum).includes(entity as LogEntityEnum)) {
      return { error: `Invalid entity type: ${entity}` };
    }

    return this.loggingService.getLogsForEntity(
      entity as LogEntityEnum,
      entityId,
      parseInt(page),
      parseInt(limit),
    );
  }

  @Get('user')
  async getLogsForCurrentUser(
    @Query('page') page = '1',
    @Query('limit') limit = '10',
    @Req() req: Request,
  ) {
    const user = req['user'];
    return this.loggingService.getLogsForUser(
      user._id,
      parseInt(page),
      parseInt(limit),
    );
  }

  @Get('organization')
  async getLogsForOrganization(
    @Query('page') page = '1',
    @Query('limit') limit = '10',
    @Req() req: Request,
  ) {
    const user = req['user'];
    const orgId = user.org_id;
    
    if (!orgId) {
      return { error: 'User does not belong to an organization' };
    }
    
    return this.loggingService.getLogsForOrganization(
      orgId,
      parseInt(page),
      parseInt(limit),
    );
  }
}
