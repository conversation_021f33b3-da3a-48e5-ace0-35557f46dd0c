import { Document, Types } from 'mongoose';
import { BaseInterface } from 'src/common/base.interface';
import { LogActionEnum } from 'src/common/enums/LogActionEnum';
import { LogEntityEnum } from 'src/common/enums/LogEntityEnum';

export interface Log extends Document, BaseInterface {
  action: LogActionEnum;
  entity: LogEntityEnum;
  entityId: Types.ObjectId;
  userId: Types.ObjectId;
  orgId?: Types.ObjectId;
  details: any;
  timestamp: Date;
}
