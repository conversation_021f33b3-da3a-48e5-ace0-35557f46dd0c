import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { OrganizationsModel } from '../organizations/organization.model';
import { LogActionEnum } from 'src/common/enums/LogActionEnum';
import { LogEntityEnum } from 'src/common/enums/LogEntityEnum';
import { Log } from './log.interface';

@Injectable()
export class LoggingService {
  private readonly logger = new Logger(LoggingService.name);

  constructor(
    @InjectModel('logs') private readonly logModel: Model<Log>,
    private readonly organizationsModel: OrganizationsModel,
  ) {}

  /**
   * Create a log entry in the database
   */
  async createLog(
    action: LogActionEnum,
    entity: LogEntityEnum,
    entityId: string | Types.ObjectId,
    userId: string | Types.ObjectId,
    details: any = {},
    orgId?: string,
  ): Promise<Log> {
    try {
      const logData: Partial<Log> = {
        action,
        entity,
        entityId: typeof entityId === 'string' ? new Types.ObjectId(entityId) : entityId,
        userId: typeof userId === 'string' ? new Types.ObjectId(userId) : userId,
        details,
        timestamp: new Date(),
      };

      // If orgId is provided as auth0_id, convert it to MongoDB ObjectId
      if (orgId) {
        const organization = await this.organizationsModel.findOne({ auth0_id: orgId });
        if (organization) {
          logData.orgId = new Types.ObjectId(organization._id);
        }
      }

      const log = new this.logModel(logData);
      const savedLog = await log.save();
      
      this.logger.log(`Log created: ${action} ${entity} ${entityId}`);
      return savedLog;
    } catch (error) {
      this.logger.error(`Error creating log: ${error.message}`, error.stack);
      // Don't throw the error - logging should not interrupt the main flow
      return null;
    }
  }

  /**
   * Log alert zone status change
   */
  async logAlertZoneStatusChange(
    alertZoneId: string,
    isActive: boolean,
    userId: string,
    alertZoneName: string,
    orgId?: string,
  ): Promise<Log> {
    const action = isActive ? LogActionEnum.ACTIVATE : LogActionEnum.DEACTIVATE;
    const details = {
      name: alertZoneName,
      status: isActive ? 'active' : 'inactive',
    };

    return this.createLog(
      action,
      LogEntityEnum.ALERT_ZONE,
      alertZoneId,
      userId,
      details,
      orgId,
    );
  }

  /**
   * Get logs for a specific entity
   */
  async getLogsForEntity(
    entity: LogEntityEnum,
    entityId: string,
    page = 1,
    limit = 10,
  ): Promise<{ logs: Log[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;
    
    const [logs, total] = await Promise.all([
      this.logModel
        .find({ entity, entityId: new Types.ObjectId(entityId) })
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .populate('userId', 'name email')
        .exec(),
      this.logModel.countDocuments({ entity, entityId: new Types.ObjectId(entityId) }),
    ]);

    return {
      logs,
      total,
      page,
      limit,
    };
  }

  /**
   * Get logs for a specific user
   */
  async getLogsForUser(
    userId: string,
    page = 1,
    limit = 10,
  ): Promise<{ logs: Log[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;
    
    const [logs, total] = await Promise.all([
      this.logModel
        .find({ userId: new Types.ObjectId(userId) })
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.logModel.countDocuments({ userId: new Types.ObjectId(userId) }),
    ]);

    return {
      logs,
      total,
      page,
      limit,
    };
  }

  /**
   * Get logs for a specific organization
   */
  async getLogsForOrganization(
    orgId: string,
    page = 1,
    limit = 10,
  ): Promise<{ logs: Log[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;
    
    // Find the organization by auth0_id
    const organization = await this.organizationsModel.findOne({ auth0_id: orgId });
    if (!organization) {
      return {
        logs: [],
        total: 0,
        page,
        limit,
      };
    }

    const [logs, total] = await Promise.all([
      this.logModel
        .find({ orgId: organization._id })
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .populate('userId', 'name email')
        .exec(),
      this.logModel.countDocuments({ orgId: organization._id }),
    ]);

    return {
      logs,
      total,
      page,
      limit,
    };
  }
}
