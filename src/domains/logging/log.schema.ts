import { Schema } from 'mongoose';
import { BaseSchemaFields } from 'src/common/base.schema';
import { LogActionEnum } from 'src/common/enums/LogActionEnum';
import { LogEntityEnum } from 'src/common/enums/LogEntityEnum';

const LogSchema = new Schema({
  action: {
    type: String,
    enum: Object.values(LogActionEnum),
    required: true
  },
  entity: {
    type: String,
    enum: Object.values(LogEntityEnum),
    required: true
  },
  entityId: { type: Schema.Types.ObjectId, required: true },
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  orgId: { type: Schema.Types.ObjectId, ref: 'organizations', required: false },
  details: { type: Schema.Types.Mixed },
  timestamp: { type: Date, default: Date.now },
});

// Create indexes for better query performance
LogSchema.index({ entity: 1, entityId: 1 });
LogSchema.index({ userId: 1 });
LogSchema.index({ orgId: 1 });
LogSchema.index({ timestamp: -1 });
LogSchema.index({ action: 1 });

export default LogSchema;
