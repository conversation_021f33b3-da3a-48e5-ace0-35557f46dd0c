import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LoggingService } from './logging.service';
import LogSchema from './log.schema';
import { OrganizationsModel } from '../organizations/organization.model';
import OrganizationSchema from '../organizations/organization.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'logs', schema: LogSchema },
      { name: 'organizations', schema: OrganizationSchema },
    ]),
  ],
  providers: [LoggingService, OrganizationsModel],
  exports: [LoggingService],
})
export class LoggingModule {}
