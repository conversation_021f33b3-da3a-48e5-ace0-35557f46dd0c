require('dotenv').config();
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import Constants from 'src/common/constants';
import SpectrumEventSchema from 'src/domains/spectrumEvents/spectrumEvent.schema';
import SpectrumEventHistorySchema from 'src/domains/spectrumEvents/spectrumEventHistory.schema';

@Module({
  imports: [
    // Separate MongoDB connection for Spectrum Events
    MongooseModule.forRoot(process.env.SPECTRUM_MONGODB_CONNECTION_STRING, {
      dbName: process.env.SPECTRUM_DATABASE_NAME,
      connectionName: 'spectrum', // Named connection for spectrum events
    }),
    MongooseModule.forFeature([
      {
        name: Constants.spectrum_profile_events,
        schema: SpectrumEventSchema,
        collection: Constants.spectrum_profile_events
      },
      {
        name: Constants.spectrum_history_events,
        schema: SpectrumEventHistorySchema,
        collection: Constants.spectrum_history_events
      },
    ], 'spectrum'), // Use the named connection
  ],
  exports: [MongooseModule],
})
export class SpectrumDatabaseModule {}
