/**
 * Cache utility functions for environment-specific cache keys
 */

/**
 * Generates an environment-specific cache key
 * @param baseKey - The base cache key
 * @returns Environment-prefixed cache key (unless in production)
 */
export function generateCacheKey(baseKey: string): string {
  const environment = process.env.ENV;
  
  // In production, use the base key as-is
  if (environment === 'prod') {
    return baseKey;
  }
  
  // For all other environments, prefix with environment
  if (environment) {
    return `${environment}:${baseKey}`;
  }
  
  // Fallback if ENV is not set (shouldn't happen in normal operation)
  return `dev:${baseKey}`;
}

/**
 * Cache key patterns for different services
 */
export const CacheKeyPatterns = {
  // User cache keys
  USER_BY_SUB: (sub: string) => generateCacheKey(`user:sub:${sub}`),
  
  // Drone authorization cache keys
  DRONE_AUTH_BY_DRONE_ID: (droneId: string) => generateCacheKey(`drone:auth:${droneId}`),
  DRONE_AUTH_BY_ID: (id: string) => generateCacheKey(`drone:auth:id:${id}`),
  DRONE_AUTH_LATEST: (droneId: string) => generateCacheKey(`drone:auth:latest:${droneId}`),
  DRONE_AUTH_BY_ORG_ID_AND_DEVICE_ID: (orgId: string, deviceId: string) => generateCacheKey(`authorized_drone:org_id:${orgId}:device_id:${deviceId}`),
  
  // Service zone cache keys
  SERVICE_ZONE_BY_ID: (id: string) => generateCacheKey(`servicezone:id:${id}`),
  SERVICE_ZONE_BY_H3: (h3Indexes: string[]) => {
    const sortedIndexes = h3Indexes.sort();
    return generateCacheKey(`servicezone:h3:${sortedIndexes.join(':')}`);
  },
  
  // Organization cache keys (already implemented)
  ORG_BY_AUTH0_ID: (auth0Id: string) => generateCacheKey(`org:auth0:${auth0Id}`),

  // Drone cache keys
  AUTHORIZED_DRONES_BY_ORG_ID: (orgId: string) => generateCacheKey(`drone:authorized:org:${orgId}`),

  // Event Profile cache keys
  EVENT_PROFILE_BY_ID: (eventId: string) => generateCacheKey(`event:profile:id:${eventId}`),
  EVENT_PROFILE_BY_DEVICE_ID: (deviceId: string) => generateCacheKey(`event:profile:device:${deviceId}`),

  // Event History cache keys
  EVENT_HISTORY_BY_EVENT_ID: (eventId: string, limit?: number) =>
    generateCacheKey(`event:history:event:${eventId}${limit ? `:limit:${limit}` : ''}`),
  EVENT_HISTORY_BY_DEVICE_ID: (deviceId: string, limit?: number) =>
    generateCacheKey(`event:history:device:${deviceId}${limit ? `:limit:${limit}` : ''}`),
  EVENT_HISTORY_LATEST_BY_EVENT_ID: (eventId: string) =>
    generateCacheKey(`event:history:latest:${eventId}`),
} as const;
