import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class AppSyncService {
  private readonly logger = new Logger(AppSyncService.name);
  private readonly appsyncUrl = process.env.APP_SYNC_URL;
  private readonly apiKey = process.env.APP_SYNC_API_KEY;

  private readonly mutationQuery = `
    mutation publish($name: String!, $data: AWSJSON!) {
      publish(name: $name, data: $data) {
        name
        data
      }
    }
  `;

  async publishMessage(name: string, data: any): Promise<void> {
    const requestBody = JSON.stringify({
      query: this.mutationQuery,
      variables: { name: name, data: JSON.stringify(data) },
    });

    try {
      const response = await axios.post(this.appsyncUrl, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
        },
      });

      const responseData = await response.data;
      this.logger.log('Response from AppSync:', responseData);
    } catch (error) {
      this.logger.error('Error publishing message to AppSync:', error);
    }
  }
}