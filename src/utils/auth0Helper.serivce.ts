import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { Cache } from 'cache-manager';

@Injectable()
export class Auth0HelperService {

  private readonly logger = new Logger(Auth0HelperService.name);
    
  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

async getManagementTokenResponse() {
    const cacheKey = 'auth0_management_token';
    
    try {
      // Try to get token from Redis cache
      const cachedToken = process.env.ENABLE_CACHE === 'true'? await this.cacheManager.get<string>(cacheKey): undefined;

      if (cachedToken && process.env.ENABLE_CACHE === 'true') {
        this.logger.log('Using cached Auth0 management token');
        return JSON.parse(cachedToken);
      }

      // If not in cache, fetch a new token
      this.logger.log('Fetching new Auth0 management token');
      const response = await axios.post(
        `${process.env.AUTH0_MANAGEMENT_API}/oauth/token`,
        {
          client_id: process.env.AUTH0_MANAGEMENT_API_CLIENT_ID,
          client_secret: process.env.AUTH0_MANAGEMENT_API_CLIENT_SECRET,
          audience: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/`,
          grant_type: 'client_credentials',
        },
        {
          headers: { 'content-type': 'application/json' },
        },
      );

      // Cache the token with TTL (expires_in is in seconds)
      // Set TTL to 80% of the actual expiration to ensure we refresh before it expires
      if(process.env.ENABLE_CACHE === 'true') {
        const ttl = response.data.expires_in ? Math.floor(response.data.expires_in * 0.8) : 86400; // Default 24h if no expiration
        await this.cacheManager.set(cacheKey, JSON.stringify(response.data), ttl);
      }

      return response.data;
    } catch (error) {
      this.logger.error('Error getting management token', error);
      // If Redis fails, fall back to direct API call
      return await axios.post(
        `${process.env.AUTH0_MANAGEMENT_API}/oauth/token`,
        {
          client_id: process.env.AUTH0_MANAGEMENT_API_CLIENT_ID,
          client_secret: process.env.AUTH0_MANAGEMENT_API_CLIENT_SECRET,
          audience: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/`,
          grant_type: 'client_credentials',
        },
        {
          headers: { 'content-type': 'application/json' },
        },
      );
    }
  }

}