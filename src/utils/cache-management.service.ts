import { Injectable, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CacheManagementService {
  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  /**
   * Clear all cache entries using pattern matching if available
   * @param patterns - Array of cache key patterns to clear
   * @returns Object with success status and cleared cache count
   */
  async clearCacheByPatterns(patterns: string[]): Promise<{ success: boolean; message: string; clearedKeys: string[] }> {
    const clearedKeys: string[] = [];

    try {
      // Try to access the underlying Redis store for pattern-based clearing
      const stores = (this.cacheManager as any).stores;
      if (stores && stores.length > 0) {
        const store = stores[0];
        
        // If it's a Keyv store with Redis, we can use scan
        if (store && store.redis) {
          const redis = store.redis;
          
          for (const pattern of patterns) {
            try {
              const keys = await redis.keys(pattern);
              if (keys && keys.length > 0) {
                await redis.del(...keys);
                clearedKeys.push(...keys);
              }
            } catch (patternError) {
              console.warn(`Failed to clear cache pattern ${pattern}: ${patternError.message}`);
            }
          }
        } else {
          console.warn('Redis store not available for pattern-based cache clearing');
        }
      }

      return {
        success: true,
        message: `Successfully cleared ${clearedKeys.length} cache entries`,
        clearedKeys
      };

    } catch (error) {
      console.error('Failed to clear cache by patterns:', error);
      return {
        success: false,
        message: `Failed to clear cache: ${error.message}`,
        clearedKeys
      };
    }
  }

  /**
   * Clear specific cache keys
   * @param keys - Array of specific cache keys to clear
   * @returns Object with success status and cleared cache count
   */
  async clearSpecificKeys(keys: string[]): Promise<{ success: boolean; message: string; clearedKeys: string[] }> {
    const clearedKeys: string[] = [];

    try {
      for (const key of keys) {
        try {
          await this.cacheManager.del(key);
          clearedKeys.push(key);
        } catch (error) {
          console.warn(`Failed to clear cache key ${key}: ${error.message}`);
        }
      }

      return {
        success: true,
        message: `Successfully cleared ${clearedKeys.length} cache entries`,
        clearedKeys
      };

    } catch (error) {
      console.error('Failed to clear specific cache keys:', error);
      return {
        success: false,
        message: `Failed to clear cache: ${error.message}`,
        clearedKeys
      };
    }
  }

  /**
   * Reset the entire cache
   * @returns Object with success status
   */
  async resetAllCache(): Promise<{ success: boolean; message: string }> {
    try {
      // Try to call reset if it exists
      if ((this.cacheManager as any).reset) {
        await (this.cacheManager as any).reset();
        return {
          success: true,
          message: 'Successfully reset entire cache'
        };
      }

      // Fallback: try to clear via Redis directly
      const stores = (this.cacheManager as any).stores;
      if (stores && stores.length > 0) {
        const store = stores[0];
        if (store && store.redis) {
          await store.redis.flushdb();
          return {
            success: true,
            message: 'Successfully flushed Redis cache database'
          };
        }
      }

      return {
        success: false,
        message: 'Cache reset not supported by current cache implementation'
      };

    } catch (error) {
      console.error('Failed to reset cache:', error);
      return {
        success: false,
        message: `Failed to reset cache: ${error.message}`
      };
    }
  }

  /**
   * Get cache statistics if available
   * @returns Object with cache statistics
   */
  async getCacheStats(): Promise<{ success: boolean; stats?: any; message: string }> {
    try {
      const stores = (this.cacheManager as any).stores;
      if (stores && stores.length > 0) {
        const store = stores[0];
        if (store && store.redis) {
          const redis = store.redis;
          const info = await redis.info('memory');
          const keyspace = await redis.info('keyspace');
          
          return {
            success: true,
            stats: {
              memory: info,
              keyspace: keyspace,
              timestamp: new Date().toISOString()
            },
            message: 'Cache statistics retrieved successfully'
          };
        }
      }

      return {
        success: false,
        message: 'Cache statistics not available for current cache implementation'
      };

    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return {
        success: false,
        message: `Failed to get cache stats: ${error.message}`
      };
    }
  }
}
