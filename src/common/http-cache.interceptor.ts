import { Injectable, ExecutionContext, Inject, CallHandler } from '@nestjs/common';
import { CACHE_MANAGER, CacheInterceptor } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';

@Injectable()
export class HttpCacheInterceptor extends CacheInterceptor {

  constructor(
    @Inject(CACHE_MANAGER) public readonly cacheManager: Cache,
  ) {
    super(cacheManager, new Reflector());
  }

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();

    if (['POST', 'PUT', 'DELETE'].includes(request.method)) {
      await this.cacheManager.del(`cache:GET:${request.originalUrl}:${request.user['sub']}`);
    }

    return super.intercept(context, next);
  }

  trackBy(context: ExecutionContext): string | undefined {
    const request = context.switchToHttp().getRequest();

    // Example: generate key based on method + URL
    if (request.method !== 'GET') {
      return undefined; // only cache GET requests
    }

    const user = `${request.user['sub']}`;

    if(request)
    return `cache:GET:${request.originalUrl}:${user}`;
  }
}
