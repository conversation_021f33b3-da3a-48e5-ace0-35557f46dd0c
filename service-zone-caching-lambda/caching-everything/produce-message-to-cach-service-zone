#!/bin/bash

export PATH=$(brew --prefix kafka)/bin:$PATH

export CLASSPATH='aws-msk-iam-auth-2.2.0-all.jar'

BOOTSTRAP_SERVER='b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198'

for i in {0..10}; do
    cat cach-service-zone-message-$i.txt | kafka-console-producer \
    --bootstrap-server $BOOTSTRAP_SERVER \
    --topic SERVICE_ZONE_CACHE_DEV \
    --producer.config 'client.properties' 
    echo "Message $i sent" 
done

# kafka-console-producer \
#     --bootstrap-server 'b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198' \
#     --topic NOTIFICATION_DEV \
#     --producer.config 'client.properties' \
#     --property parse.key=true \
#     --property key.separator=: