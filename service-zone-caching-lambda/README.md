# Service Zone H3 Mapping Caching Lambda

This Lambda function connects to MongoDB, fetches the `servicezone_h3map` collection, and caches H3 to FIPS mappings using Redis/Valkey through the CacheManager and CacheMonitor classes.

## Features

- **MongoDB Connection**: Uses `DatabaseConnection.py` to establish a singleton connection to MongoDB
- **Cache Management**: Uses `CacheManager.py` to interact with Redis/Valkey cache
- **Cache Monitoring**: Uses `CacheMonitor.py` to track cache performance and health
- **Kafka Event Processing**: Processes Kafka events that can trigger cache refresh
- **Error Handling**: Comprehensive error handling and logging
- **Performance Monitoring**: Tracks cache hits, misses, and performance metrics

## Files

- `app.py` - Main Lambda handler and caching logic
- `DatabaseConnection.py` - MongoDB connection singleton class
- `CacheManager.py` - Redis/Valkey cache management class
- `CacheMonitor.py` - Cache performance monitoring and health checks
- `test_app.py` - Test script to demonstrate functionality
- `requirements.txt` - Python dependencies
- `dockerfile` - Docker configuration for deployment

## Environment Variables

The application uses the following environment variables:

### MongoDB Configuration

- `MONGODB_CONNECTION_STRING` - MongoDB connection string (default: provided connection string)
- `DATABASE_NAME` - Database name (default: "coddn")

### Redis/Valkey Configuration

- `REDIS_HOST` - Redis host (default: "master.coddn-valkey.9nthzl.use2.cache.amazonaws.com")
- `REDIS_USERNAME` - Redis username (default: "valkey-connect")
- `REDIS_PASSWORD` - Redis password (default: provided password)

## Data Structure

The application works with H3 to FIPS mapping data in the following structure:

```json
{
  "_id": "87260e535ffffff",
  "fips": ["31039"]
}
```

Where:

- `_id`: H3 index (used as cache key)
- `fips`: Array of FIPS codes (cached as the value)

## Functionality

### 1. Fetch and Cache H3 Mappings

The `fetch_and_cache_service_zones()` function:

- Connects to MongoDB using `DatabaseConnection`
- Fetches all documents from the `servicezone_h3map` collection
- Caches each mapping with H3 ID as key and FIPS array as value
- Uses `CacheMonitor` to track performance metrics
- Performs health checks on the cache system

### 2. Cache Retrieval

The `get_cached_h3_mapping(h3_id)` function:

- Retrieves FIPS data for a specific H3 ID
- Logs cache hits and misses
- Returns FIPS array or None if not found

### 3. Test Cache Retrieval

The `test_cache_retrieval()` function:

- Tests cache retrieval with sample H3 IDs
- Returns results for multiple H3 mappings

### 3. Lambda Handler

The `lambda_handler()` function:

- Processes Kafka events
- Triggers cache refresh operations
- Returns status information including cache metrics

## Cache Keys

- `{h3_id}` - H3 index as key (e.g., "87260e535ffffff")
- Value: FIPS array (e.g., ["31039"])

## Cache TTL

- Default TTL: 3600 seconds (1 hour)
- Can be modified in the code as needed

## Usage

### Running Locally

```bash
# Install dependencies
pip install -r requirements.txt

# Run test script
python test_app.py
```

### Lambda Deployment

1. Package the code with dependencies
2. Deploy to AWS Lambda
3. Configure environment variables
4. Set up Kafka trigger if needed

## Monitoring

The `CacheMonitor` class provides:

- Cache hit/miss tracking
- Performance statistics
- Health checks
- Error logging
- Memory usage monitoring (when available)

### Performance Metrics

- Hit rate percentage
- Total operations count
- Error count
- Uptime tracking
- Redis server statistics

### Health Checks

- Connection testing
- Hit rate analysis
- Error rate monitoring
- Memory usage alerts

## Error Handling

The application includes comprehensive error handling for:

- MongoDB connection failures
- Redis/Valkey connection issues
- Data serialization errors
- Cache operation failures
- General exceptions

All errors are logged with appropriate detail levels.

## Testing

Use the `test_app.py` script to test the functionality:

- Tests database connection and caching
- Tests cache retrieval
- Tests Lambda handler with sample Kafka event
- Displays performance metrics and health status

## Dependencies

- `pymongo==4.6.1` - MongoDB driver
- `redis==5.0.1` - Redis client library

## Notes

- The application uses a singleton pattern for database connections
- Cache operations are atomic and include proper error handling
- Performance monitoring is built-in and provides detailed metrics
- The code is designed to work in AWS Lambda environment
- All sensitive credentials should be managed through environment variables or AWS Secrets Manager
