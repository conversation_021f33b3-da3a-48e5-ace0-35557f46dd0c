const axios = require('axios');
const fs = require('fs');
const path = require('path');

const FAA_API_BASE_URL = 'https://uasdoc.faa.gov/api/v1/publicDOCRev';
const ITEMS_PER_PAGE = 1000;
const OUTPUT_FILE = path.join(__dirname, '..', 'data', 'faa-uas-data.json');

async function fetchAllFaaData() {
  console.log('🚁 Starting FAA UAS data fetch...');
  
  try {
    let allData = [];
    let pageIndex = 0;
    let totalItems = 0;
    let hasMoreData = true;

    // Create data directory if it doesn't exist
    const dataDir = path.dirname(OUTPUT_FILE);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    while (hasMoreData) {
      console.log(`📄 Fetching page ${pageIndex + 1}...`);
      
      const response = await axios.get(FAA_API_BASE_URL, {
        params: {
          itemsPerPage: ITEMS_PER_PAGE,
          pageIndex: pageIndex,
          search: '' // Empty search to get all data
        },
        timeout: 30000 // 30 second timeout
      });

      const data = response.data;
      
      if (pageIndex === 0) {
        totalItems = data.data.totalItems;
        console.log(`📊 Total items to fetch: ${totalItems}`);
      }

      if (data.data.items && data.data.items.length > 0) {
        allData = allData.concat(data.data.items);
        console.log(`✅ Fetched ${data.data.items.length} items (Total: ${allData.length}/${totalItems})`);
        
        pageIndex++;
        
        // Check if we have more data to fetch
        const currentItemCount = data.data.currentItemCount;
        const startIndex = data.data.startIndex;
        hasMoreData = (startIndex + currentItemCount) < totalItems;
      } else {
        hasMoreData = false;
      }

      // Add a small delay to be respectful to the API
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Create a lookup object for faster searches
    const lookupData = {
      lastUpdated: new Date().toISOString(),
      totalRecords: allData.length,
      data: {},
      rawData: allData
    };

    // Create lookup by tracking number, make, model, and series
    allData.forEach(item => {
      // Index by various possible identifiers
      if (item.trackingNumber) {
        lookupData.data[item.trackingNumber.toLowerCase()] = item;
      }
      if (item.makeName) {
        lookupData.data[item.makeName.toLowerCase()] = item;
        // Also create combined keys for better searching
        if (item.modelName) {
          lookupData.data[`${item.makeName.toLowerCase()}_${item.modelName.toLowerCase()}`] = item;
        }
      }
      if (item.modelName) {
        lookupData.data[item.modelName.toLowerCase()] = item;
      }
      if (item.series) {
        lookupData.data[item.series.toLowerCase()] = item;
      }
    });

    // Save to JSON file
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(lookupData, null, 2));
    
    console.log(`🎉 Successfully fetched and saved ${allData.length} FAA UAS records to ${OUTPUT_FILE}`);
    console.log(`📈 Created lookup index with ${Object.keys(lookupData.data).length} searchable keys`);
    
    return {
      success: true,
      totalRecords: allData.length,
      filePath: OUTPUT_FILE
    };

  } catch (error) {
    console.error('❌ Error fetching FAA data:', error.message);
    
    // If there's an existing file, keep it and just log the error
    if (fs.existsSync(OUTPUT_FILE)) {
      console.log('📁 Keeping existing FAA data file due to fetch error');
      return {
        success: false,
        error: error.message,
        keptExistingFile: true
      };
    }
    
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  fetchAllFaaData()
    .then(result => {
      if (result.success) {
        console.log('✨ FAA data fetch completed successfully!');
        process.exit(0);
      } else {
        console.log('⚠️  FAA data fetch completed with warnings');
        process.exit(0);
      }
    })
    .catch(error => {
      console.error('💥 FAA data fetch failed:', error.message);
      process.exit(1);
    });
}

module.exports = { fetchAllFaaData };
