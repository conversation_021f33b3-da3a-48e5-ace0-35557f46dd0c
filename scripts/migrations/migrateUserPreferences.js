/* eslint-disable */
const mongoose = require('mongoose');

// commented const uri out for now
const dbName = "coddn";
mongoose.connect(uri, {
  dbName: dbName,
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const userPreferencesSchema = new mongoose.Schema({}, { strict: false });
const UserPreferences = mongoose.model('userpreferences', userPreferencesSchema);

async function migrateUserPreferences() {
  try {
    console.log('Starting user preferences migration to new structure...\n');
    
    const docs = await UserPreferences.find({});
    console.log(`Found ${docs.length} documents to migrate\n`);
    
    let migratedCount = 0;
    
    for (const doc of docs) {
      let hasUpdates = false;
      const updateData = {};

      // Field name mapping
      if (doc.showSateliteView !== undefined) {
        updateData.showSatelliteViewByDefault = doc.showSateliteView;
        hasUpdates = true;
        console.log(`Migrating showSateliteView → showSatelliteViewByDefault`);
      }
      
      if (doc.showDroneTraffic !== undefined) {
        updateData.showAllDroneTraffic = doc.showDroneTraffic;
        hasUpdates = true;
        console.log(`Migrating showDroneTraffic → showAllDroneTraffic`);
      }

      // Set defaults if fields don't exist
      if (doc.showSatelliteViewByDefault === undefined && updateData.showSatelliteViewByDefault === undefined) {
        updateData.showSatelliteViewByDefault = true;
        hasUpdates = true;
      }

      // rename showAlertZones -> showAlertZoneBoundaries
      if (doc.showAlertZones !== undefined) {
        updateData.showAlertZoneBoundaries = doc.showAlertZones;
        hasUpdates = true;
        console.log(`Migrating showAlertZones → showAlertZoneBoundaries`);
      } else if (doc.showAlertZoneBoundaries === undefined) {
        updateData.showAlertZoneBoundaries = true;
        hasUpdates = true;
      }

      // Add new showInactiveAlertZones field with default true
      if (doc.showInactiveAlertZones === undefined) {
        updateData.showInactiveAlertZones = true;
        hasUpdates = true;
        console.log(`Adding showInactiveAlertZones field with default true`);
      }

      if (doc.showAllDroneTraffic === undefined && updateData.showAllDroneTraffic === undefined) {
        updateData.showAllDroneTraffic = true;
        hasUpdates = true;
      }

      if (doc.loadInitialPageOnRegionView === undefined) {
        updateData.loadInitialPageOnRegionView = true;
        hasUpdates = true;
      }

      // Create new alerts structure
      if (!doc.alerts || typeof doc.alerts !== 'object') {
        const alerts = {};
        alerts.droneEvent = true;
        alerts.alertZoneStatus = true;
        updateData.alerts = alerts;
        hasUpdates = true;
        console.log(`Creating alerts structure`);
      }

      // Create new delivery structure from emailAlert/smsAlert
      if (!doc.delivery || typeof doc.delivery !== 'object') {
        const delivery = {};
        
        const globalEmail = doc.emailAlert !== undefined ? doc.emailAlert : true;
        const globalSms = doc.smsAlert !== undefined ? doc.smsAlert : false;
        
        delivery.droneEvent = { email: globalEmail, sms: globalSms };
        delivery.alertZoneStatus = { email: globalEmail, sms: globalSms };
        
        updateData.delivery = delivery;
        hasUpdates = true;
        console.log(`Creating delivery structure from emailAlert/smsAlert`);
      }

      // Clean up old fields
      const unsetFields = {};
      
      if (doc.showSateliteView !== undefined) {
        unsetFields.showSateliteView = "";
      }
      if (doc.showDroneTraffic !== undefined) {
        unsetFields.showDroneTraffic = "";
      }
      if (doc.showAlertZones !== undefined) {
        unsetFields.showAlertZones = "";
      }
      if (doc.emailAlert !== undefined) {
        unsetFields.emailAlert = "";
      }
      if (doc.smsAlert !== undefined) {
        unsetFields.smsAlert = "";
      }
      if (doc.showSateliteViewOnMapByDefault !== undefined) {
        unsetFields.showSateliteViewOnMapByDefault = "";
      }
      if (doc.showSatelliteViewOnMap !== undefined) {
        unsetFields.showSatelliteViewOnMap = "";
      }
      if (doc.showAllAlertZones !== undefined) {
        unsetFields.showAllAlertZones = "";
      }
      if (doc.showAllDronesTraffic !== undefined) {
        unsetFields.showAllDronesTraffic = "";
      }
      if (doc.droneEvent !== undefined) {
        unsetFields.droneEvent = "";
      }
      if (doc.alertZoneStatus !== undefined) {
        unsetFields.alertZoneStatus = "";
      }
      if (doc.droneEventSms !== undefined) {
        unsetFields.droneEventSms = "";
      }
      if (doc.droneEventEmail !== undefined) {
        unsetFields.droneEventEmail = "";
      }
      if (doc.alertZoneStatusSms !== undefined) {
        unsetFields.alertZoneStatusSms = "";
      }
      if (doc.alertZoneStatusEmail !== undefined) {
        unsetFields.alertZoneStatusEmail = "";
      }
      if (doc.textAlerts !== undefined) {
        unsetFields.textAlerts = "";
      }
      if (doc.emailAlerts !== undefined) {
        unsetFields.emailAlerts = "";
      }
      
      if (Object.keys(unsetFields).length > 0) {
        updateData.$unset = unsetFields;
        hasUpdates = true;
        console.log(`Cleaning up old fields`);
      }

      // Execute update
      if (hasUpdates) {
        const result = await UserPreferences.updateOne(
          { _id: doc._id },
          updateData
        );
        
        if (result.modifiedCount > 0) {
          migratedCount++;
          console.log(`Migrated document ${doc._id}`);
        }
      }
    }
    
    console.log(`\nMigration completed! Successfully migrated ${migratedCount} documents`);
    
    // Step 2: Verify migration
    console.log('\nVerifying migration results...');
    const verifyDocs = await UserPreferences.find({});
    let validDocs = 0;
    
    for (const doc of verifyDocs) {
      if (doc.alerts && doc.delivery && 
          typeof doc.alerts === 'object' && typeof doc.delivery === 'object') {
        validDocs++;
      }
    }
    
    console.log(`Verification: ${validDocs}/${verifyDocs.length} documents have valid new structure`);
    
    if (validDocs === verifyDocs.length) {
      console.log('\nSUCCESS: All documents successfully migrated to new structure!');
    } else {
      console.log('\nWARNING: Some documents may need manual review');
    }
    
  } catch (error) {
    console.error('ERROR during migration:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

// Run the migration
migrateUserPreferences();