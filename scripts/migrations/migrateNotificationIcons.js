/* eslint-disable */
const mongoose = require('mongoose');

// commented const uri out for now
const dbName = "coddn";
mongoose.connect(uri, {
  dbName: dbName,
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Icon mapping from old values to new URLs
const ICON_MAPPING = {
  'status-alert': 'https://dev.aerodefense.tech/assets/SystemNotif-C2CQOIQa.svg',
  'online-status-alert': 'https://dev.aerodefense.tech/assets/ActiveZone-_9ZmR9Zd.svg',
  'offline-status-alert': 'https://dev.aerodefense.tech/assets/InactiveZone-sDL_UzmB.svg',
};

const systemNotificationSchema = new mongoose.Schema({}, { strict: false });
const SystemNotification = mongoose.model('system_notification', systemNotificationSchema);

async function migrateNotificationIcons() {
  try {
    console.log('Starting notification icon migration...\n');
    
    // Use bulk operations for better performance
    let totalMigrated = 0;
    
    // Process each icon type separately using bulk updates
    for (const [oldIcon, newUrl] of Object.entries(ICON_MAPPING)) {
      console.log(`Processing "${oldIcon}" -> "${newUrl}"`);
      
      // Find all documents with this specific icon value
      const docsToUpdate = await SystemNotification.find({ icon: oldIcon });
      console.log(`Found ${docsToUpdate.length} documents with "${oldIcon}"`);
      
      if (docsToUpdate.length > 0) {
        // Use bulkWrite for better performance
        const bulkOps = docsToUpdate.map(doc => ({
          updateOne: {
            filter: { _id: doc._id },
            update: { $set: { icon: newUrl } }
          }
        }));
        
        // Process in batches of 1000
        const batchSize = 1000;
        for (let i = 0; i < bulkOps.length; i += batchSize) {
          const batch = bulkOps.slice(i, i + batchSize);
          const result = await SystemNotification.bulkWrite(batch);
          totalMigrated += result.modifiedCount;
          console.log(`Updated batch ${Math.floor(i/batchSize) + 1}: ${result.modifiedCount} documents`);
        }
      }
    }
    
    // Handle empty string icons (set to default)
    console.log('\nProcessing empty string icons...');
    const emptyIconDocs = await SystemNotification.find({ icon: '' });
    console.log(`Found ${emptyIconDocs.length} documents with empty icon`);
    
    if (emptyIconDocs.length > 0) {
      const defaultUrl = 'https://dev.aerodefense.tech/assets/SystemNotif-C2CQOIQa.svg';
      const bulkOps = emptyIconDocs.map(doc => ({
        updateOne: {
          filter: { _id: doc._id },
          update: { $set: { icon: defaultUrl } }
        }
      }));
      
      const batchSize = 1000;
      for (let i = 0; i < bulkOps.length; i += batchSize) {
        const batch = bulkOps.slice(i, i + batchSize);
        const result = await SystemNotification.bulkWrite(batch);
        totalMigrated += result.modifiedCount;
        console.log(`Updated empty icon batch ${Math.floor(i/batchSize) + 1}: ${result.modifiedCount} documents`);
      }
    }
    
    console.log(`\nMigration Summary:`);
    console.log(`Total documents migrated: ${totalMigrated}`);
    
    // Verification
    console.log('\nVerification:');
    const verifyDocs = await SystemNotification.find({ 
      icon: { $exists: true, $ne: null, $ne: '' }
    });
    
    let urlCount = 0;
    let textCount = 0;
    
    for (const doc of verifyDocs) {
      if (doc.icon && doc.icon.startsWith('http')) {
        urlCount++;
      } else {
        textCount++;
      }
    }
    
    console.log(`Icons now using URLs: ${urlCount}`);
    console.log(`Icons still using text: ${textCount}`);
    
    if (textCount === 0) {
      console.log('\nSUCCESS: All notification icons have been migrated to URLs!');
    } else {
      console.log(`\nWARNING: ${textCount} icons still use text values.`);
    }
    
  } catch (error) {
    console.error('ERROR during migration:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

// Run the migration
migrateNotificationIcons();
