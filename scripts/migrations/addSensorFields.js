/* eslint-disable */
const mongoose = require('mongoose');

// Database connection - you'll need to uncomment and set the URI
const uri = "mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority";
const dbName = "coddnQA";

// Connect to MongoDB
mongoose.connect(uri, {
  dbName: dbName,
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Create a flexible schema for the sensor collection
const sensorSchema = new mongoose.Schema({}, { strict: false });
const Sensor = mongoose.model('sensor_profile', sensorSchema, "sensor_profile");

async function addSensorFields() {
  try {
    console.log('Starting sensor fields migration...\n');
    console.log('Adding node_id, host_name, and time_stamp fields to sensor documents\n');
    
    // Find all sensor documents
    const sensors = await Sensor.find();
    console.log(`Found ${sensors.length} sensor documents to update\n`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    // Process each document
    for (const sensor of sensors) {
      let hasUpdates = false;
      const updateData = {};
      
      // Add node_id field from NODE_ID if it doesn't exist
      if (!sensor.node_id && sensor.NODE_ID) {
        updateData.node_id = sensor.NODE_ID;
        hasUpdates = true;
        console.log(`Adding node_id: ${sensor.NODE_ID} for document ${sensor._id}`);
      }
      
      // Add host_name field from HOST_NAME if it doesn't exist
      if (!sensor.host_name && sensor.HOST_NAME) {
        updateData.host_name = sensor.HOST_NAME;
        hasUpdates = true;
        console.log(`Adding host_name: ${sensor.HOST_NAME} for document ${sensor._id}`);
      }
      
      // Add time_stamp field from TIME_STAMP if it doesn't exist
      if (!sensor.time_stamp && sensor.TIME_STAMP) {
        updateData.time_stamp = sensor.TIME_STAMP;
        hasUpdates = true;
        console.log(`Adding time_stamp: ${sensor.TIME_STAMP} for document ${sensor._id}`);
      }
      
      // Update the document if there are changes
      if (hasUpdates) {
        const result = await Sensor.updateOne(
          { _id: sensor._id },
          { $set: updateData }
        );
        
        if (result.modifiedCount > 0) {
          updatedCount++;
          console.log(`✅ Updated document ${sensor._id}`);
        } else {
          console.log(`⚠️  Failed to update document ${sensor._id}`);
        }
      } else {
        skippedCount++;
        console.log(`⏭️  Skipped document ${sensor._id} (fields already exist or source fields missing)`);
      }
    }
    
    console.log(`\n📊 Migration Summary:`);
    console.log(`Total documents processed: ${sensors.length}`);
    console.log(`Documents updated: ${updatedCount}`);
    console.log(`Documents skipped: ${skippedCount}`);
    
    // Verification step
    console.log('\n🔍 Verifying migration results...');
    const verificationSensors = await Sensor.find({});
    let validCount = 0;
    
    for (const sensor of verificationSensors) {
      const hasNodeId = sensor.node_id !== undefined;
      const hasHostName = sensor.host_name !== undefined;
      const hasTimeStamp = sensor.time_stamp !== undefined;
      
      if (hasNodeId && hasHostName && hasTimeStamp) {
        validCount++;
      } else {
        console.log(`⚠️  Document ${sensor._id} missing fields:`, {
          node_id: hasNodeId ? '✅' : '❌',
          host_name: hasHostName ? '✅' : '❌', 
          time_stamp: hasTimeStamp ? '✅' : '❌'
        });
      }
    }
    
    console.log(`\n✅ Verification complete: ${validCount}/${verificationSensors.length} documents have all new fields`);
    
    if (validCount === updatedCount) {
      console.log('\n🎉 SUCCESS: All updated documents have the required fields!');
    } else {
      console.log('\n⚠️  WARNING: Some documents may need manual review');
    }
    
  } catch (error) {
    console.error('❌ ERROR during migration:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the migration
addSensorFields();
