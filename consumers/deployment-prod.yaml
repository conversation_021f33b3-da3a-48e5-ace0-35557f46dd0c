---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: data-handler
  namespace: prod
  labels:
    app: data-handler
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: data-handler
  template:
    metadata:
      labels:
        app: data-handler
    spec:
      containers:
        - name: data-handler
          image: 794038255033.dkr.ecr.us-east-2.amazonaws.com/prod/data-handler:latest
          ports:
            - containerPort: 3001
          env:
            - name: MONGODB_CONNECTION_STRING
              value: 'mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=AirWardenEssentials'
            - name: DATABASE_NAME
              value: 'coddn'
            - name: GROUP_ID
              value: prodGroupDataHandler
            - name: RID_UI_TOPIC
              value: DETECTION
            - name: RID_TOPIC
              value: RID
            - name: ACCESS_KEY
              value: ********************
            - name: SECRET_KEY
              value: QU0vP1ZhjhFJSG3mMI7Ep1N66gHsMp3liiq1pbYy
            - name: REGION
              value: us-east-2
            - name: BOOTSTRAP_SERVERS
              value: 'b-2-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198'
            - name: APP_SYNC_URL
              value: https://nzf6alzxl5grfhylui4srzpfka.appsync-api.us-east-2.amazonaws.com/graphql
            - name: APP_SYNC_API_KEY
              value: da2-vcdnqfsqtjgbflxmr3rnhxchba
