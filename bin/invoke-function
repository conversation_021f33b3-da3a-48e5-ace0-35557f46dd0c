#!/bin/bash

FUNCTION_NAME="Alert-Distributor"

aws lambda invoke --function-name $FUNCTION_NAME \
    --cli-binary-format raw-in-base64-out \
    --payload "{'eventSource': 'aws:kafka', 'eventSourceArn': 'arn:aws:kafka:us-east-2:399444019738:cluster/coddn/618a2e1a-dae2-4d8e-a247-f719fecf1bc4-2', 'bootstrapServers': 'b-3.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9098,b-1.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9098,b-2.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9098', 'records': {'NOTIFICATION_DEV-0': [{'topic': 'NOTIFICATION_DEV', 'partition': 0, 'offset': 246, 'timestamp': 1739639482605, 'timestampType': 'CREATE_TIME', 'value': '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'headers': []}]}}" response.json