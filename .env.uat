MSK_BROKERS='["b-2-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198"]'
MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnStaging"
DATABASE_NAME=coddn_uat

AWS_ACCESS_KEY_ID=********************
AWS_SERCRET_KEY_ID=17Bu+iP/8WZr8ii4JDzd0qxLfejV1gqzQeUA83/M
AWS_REGION=us-east-2
MSK_PYTHON_BROKERS="b-2-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198"
# not updated
AUTH0_BASE_URL=http://localhost:3000
# AUTH0_ISSUER_URL=https://dev-mquz0q4oastb8zws.us.auth0.com
AUTH0_ISSUER_URL=https://auth-demo.aerodefense.tech
AUTH0_AUDIENCE=https://api-uat.aerodefense.tech
AUTH0_CLIENT_ID=P8bDXvNuR432Wayf0ceKnXYVOXX82KHh
AUTH0_SECRET_KEY=****************************************************************

AUTH0_MANAGEMENT_API=https://dev-mquz0q4oastb8zws.us.auth0.com
AUTH0_MANAGEMENT_API_CLIENT_ID=QMGaKc2LSBZgvVB9Pdztbwj8B5X4sGL9
AUTH0_MANAGEMENT_API_CLIENT_SECRET=****************************************************************

# updated
REDIS_HOST="master.aw-e-coddn-valkey-production.e9ylmx.use2.cache.amazonaws.com"
GROUP_ID=uatGroupApi
RID_UI_TOPIC="DETECTION_UAT"
RID_TOPIC="RID_UAT"
APP_SYNC_URL='https://ywaxsm2h45axtpkc4zgjjsihnm.appsync-api.us-east-2.amazonaws.com/graphql'
APP_SYNC_API_KEY='da2-cu73cm4tqvbqde64bvtbkt5rz4'
REDIS_HOST="rediss://coddn-valkey-user1:<EMAIL>:6379"
ENV=uat
ENABLE_CACHE=false

##############SPECTRUM DATABASE CONNECTION
# SPECTRUM_MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority"

SPECTRUM_MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=aw-s-spectrum-db"
SPECTRUM_DATABASE_NAME=coddn_staging

# FEATURE_FLAGS
SPECTRUM_FLAG=true
