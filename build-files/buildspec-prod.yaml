version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 794038255033.dkr.ecr.us-east-2.amazonaws.com
      - echo Configuring AWS CLI
      - aws configure set aws_access_key_id ********************
      - aws configure set aws_secret_access_key uXN3oa2loK+DxgGpEHOImJ67Tn2hLsppJ9lps8LC
      - aws configure set region us-east-2
      - aws eks update-kubeconfig --region us-east-2 --name coddn
      - docker login --username alexmedina443 --password ************************************
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build --build-arg ENVIRONMENT=prod -t coddn-api .
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image to ECR...
      - IMAGE_TAG=$(date +%Y%m%d%H%M%S)
      - docker tag coddn-api:latest 794038255033.dkr.ecr.us-east-2.amazonaws.com/prod/coddn-api:$IMAGE_TAG
      - docker push 794038255033.dkr.ecr.us-east-2.amazonaws.com/prod/coddn-api:$IMAGE_TAG
      - kubectl apply -f deployment-files/deployment-prod.yaml
      - kubectl set image deployment/coddn-api coddn-api=794038255033.dkr.ecr.us-east-2.amazonaws.com/prod/coddn-api:$IMAGE_TAG -n prod
artifacts:
  files:
    - '**/*'
  discard-paths: yes