# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.env
__pycache__

lib
LICENSE.txt
pyvenv.cfg
consumers/Scripts
*.rdb
notificationService/Scripts/activate
notificationService/Scripts/activate.bat
notificationService/Scripts/Activate.ps1
notificationService/Scripts/deactivate.bat
notificationService/Scripts/pip.exe
notificationService/Scripts/pip3.11.exe
notificationService/Scripts/pip3.exe
notificationService/Scripts/python.exe
notificationService/Scripts/pythonw.exe
Scripts/python.exe
Scripts/pythonw.exe
yarn.lock
/consumers/src
/consumers/Include
consumers/CHANGELOG.md
consumers/CMakeLists.txt
consumers/LICENSE
consumers/makefile
consumers/pyproject.toml
consumers/readme.md
consumers/requirements.in
consumers/setup.py

mongo-backup/