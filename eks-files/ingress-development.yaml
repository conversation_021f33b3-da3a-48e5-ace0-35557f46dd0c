---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: coddn-api-dev
  namespace: development
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: instance
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
          - path: /
            pathType: Exact
            backend:
              service:
                name: coddn-api-service-dev
                port:
                  number: 80
