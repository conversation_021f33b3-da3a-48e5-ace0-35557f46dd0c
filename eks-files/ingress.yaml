---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: coddn-api
  namespace: staging
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: instance
    nginx.ingress.kubernetes.io/proxy-read-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '3600'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:region:account-id:certificate/c238b57f-8344-4b57-90a8-ef5feb68a12e # Add your certificate ARN here
spec:
  ingressClassName: alb
  rules:
    - host: coddn-api.airwarden.tech # Add your host here
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: coddn-api-service-staging
                port:
                  number: 80
  tls:
    - hosts:
        - coddn-api.airwarden.tech # Add your host here
      secretName: tls-secret # This is the secret that contains the TLS certificate
