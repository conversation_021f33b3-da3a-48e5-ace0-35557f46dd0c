# Multi-stage build for smaller final image
# Stage 1: Build stage with all dependencies
FROM node:20-bullseye AS builder

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install system dependencies required for Python packages (build stage only)
RUN apt-get update && apt-get install -y \
    git \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    libffi-dev \
    libssl-dev \
    librdkafka-dev \
    pkg-config \
    gcc \
    g++ \
    make \
    cmake \
    && rm -rf /var/lib/apt/lists/*

# Install all Node.js dependencies (including dev dependencies for build)
RUN npm ci && npm cache clean --force

# Copy source code
COPY . .

# Upgrade pip and install wheel for better package compilation
RUN python3 -m pip install --upgrade pip setuptools wheel

# Install Python dependencies
RUN python3 -m pip install -r ./pyScripts/requirements.txt

# Build the application
RUN npm run build

# Install only production dependencies for final copy
RUN npm ci --only=production && npm cache clean --force

# Stage 2: Production stage with minimal runtime dependencies
FROM node:20-bullseye-slim AS production

# Install only runtime dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    librdkafka1 \
    net-tools \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create app directory
WORKDIR /usr/src/app

# Copy built application from builder stage
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/package*.json ./

# Copy only necessary Python scripts (not the entire pyScripts directory)
COPY --from=builder /usr/src/app/pyScripts/*.py ./pyScripts/
COPY --from=builder /usr/src/app/pyScripts/requirements.txt ./pyScripts/

# Copy Python packages from builder stage
COPY --from=builder /usr/local/lib/python3.9 /usr/local/lib/python3.9
COPY --from=builder /usr/local/bin /usr/local/bin

# Argument to determine environment (default is "staging")
ARG ENVIRONMENT=staging

# Argument to set the port (default is 3000)
ARG PORT=3000

# Copy environment files
COPY .env.* ./

# Create and populate .env file based on the environment argument
RUN if [ "$ENVIRONMENT" = "dev" ]; then cp .env.dev .env; else cp .env.staging .env; fi && \
    if [ "$ENVIRONMENT" = "qa" ]; then cp .env.qa .env; fi && \
    if [ "$ENVIRONMENT" = "uat" ]; then cp .env.uat .env; fi && \
    if [ "$ENVIRONMENT" = "prod" ]; then cp .env.prod .env; fi

# Expose the port on which the app will run
EXPOSE $PORT

# Start the server using the production build
CMD ["npm", "run", "start:prod"]
